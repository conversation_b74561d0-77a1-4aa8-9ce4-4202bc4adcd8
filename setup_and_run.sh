#!/bin/bash

# CentOS虚拟机环境设置和运行脚本
# 请在CentOS虚拟机中运行此脚本

echo "=========================================="
echo "CentOS环境 - Spark分析任务设置和运行"
echo "=========================================="

# 检查是否在Linux环境
if [[ "$OSTYPE" != "linux-gnu"* ]]; then
    echo "警告: 此脚本需要在Linux环境中运行"
    echo "请将文件复制到CentOS虚拟机中执行"
fi

echo "当前用户: $(whoami)"
echo "当前目录: $(pwd)"
echo "系统信息: $(uname -a)"

# 自动检测Java
echo ""
echo "检测Java环境..."
JAVA_CANDIDATES=(
    "/usr/lib/jvm/java-1.8.0-openjdk"
    "/usr/lib/jvm/java-8-openjdk"
    "/usr/lib/jvm/java-1.8.0-openjdk-amd64"
    "/usr/lib/jvm/default-java"
    "/opt/java"
)

for java_path in "${JAVA_CANDIDATES[@]}"; do
    if [ -d "$java_path" ] && [ -x "$java_path/bin/java" ]; then
        export JAVA_HOME="$java_path"
        echo "✓ 找到Java: $JAVA_HOME"
        break
    fi
done

if [ -z "$JAVA_HOME" ]; then
    if command -v java >/dev/null 2>&1; then
        JAVA_BIN=$(which java)
        JAVA_HOME=$(dirname $(dirname $(readlink -f $JAVA_BIN 2>/dev/null || echo $JAVA_BIN)))
        export JAVA_HOME="$JAVA_HOME"
        echo "✓ 从系统推导Java: $JAVA_HOME"
    else
        echo "✗ 未找到Java，请安装OpenJDK 8"
        echo "安装命令: sudo yum install java-1.8.0-openjdk java-1.8.0-openjdk-devel"
        exit 1
    fi
fi

# 自动检测Spark
echo ""
echo "检测Spark环境..."
SPARK_CANDIDATES=(
    "/opt/spark"
    "/usr/local/spark"
    "/home/<USER>"
    "$HOME/spark"
)

for spark_path in "${SPARK_CANDIDATES[@]}"; do
    if [ -d "$spark_path" ] && [ -x "$spark_path/bin/spark-submit" ]; then
        export SPARK_HOME="$spark_path"
        echo "✓ 找到Spark: $SPARK_HOME"
        break
    fi
done

if [ -z "$SPARK_HOME" ]; then
    if command -v spark-submit >/dev/null 2>&1; then
        SPARK_BIN=$(which spark-submit)
        SPARK_HOME=$(dirname $(dirname $SPARK_BIN))
        export SPARK_HOME="$SPARK_HOME"
        echo "✓ 从系统推导Spark: $SPARK_HOME"
    else
        echo "✗ 未找到Spark"
        echo "请确保Spark已正确安装并配置"
        exit 1
    fi
fi

# 设置环境变量
export PATH=$SPARK_HOME/bin:$JAVA_HOME/bin:$PATH

echo ""
echo "环境配置完成:"
echo "  JAVA_HOME: $JAVA_HOME"
echo "  SPARK_HOME: $SPARK_HOME"

# 验证环境
echo ""
echo "验证环境..."
echo "Java版本:"
$JAVA_HOME/bin/java -version 2>&1 | head -3

echo ""
echo "Spark版本:"
$SPARK_HOME/bin/spark-submit --version 2>&1 | grep -E "(version|Version)" | head -1

# 检查必要文件
echo ""
echo "检查必要文件..."
REQUIRED_FILES=(
    "CoffeeChain.csv"
    "GeneratePeopleAge.scala"
    "CalculateAverageAge.scala"
    "CoffeeDataPreprocessing.scala"
    "CoffeeSalesRanking.scala"
    "CoffeeDistributionAnalysis.scala"
)

missing_files=0
for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file"
    else
        echo "✗ $file (缺失)"
        missing_files=$((missing_files + 1))
    fi
done

if [ $missing_files -gt 0 ]; then
    echo ""
    echo "错误: 缺少 $missing_files 个必要文件"
    echo "请确保所有Scala文件和数据文件都在当前目录中"
    exit 1
fi

echo ""
echo "所有文件检查通过，开始执行分析任务..."

# 运行函数
run_spark_job() {
    local job_name=$1
    local scala_file="${job_name}.scala"
    
    echo ""
    echo "=========================================="
    echo "运行: $job_name"
    echo "=========================================="
    
    # 使用更简单的spark-submit命令
    $SPARK_HOME/bin/spark-submit \
        --class "$job_name" \
        --master "local[*]" \
        --driver-memory 1g \
        --executor-memory 512m \
        "$scala_file"
    
    local result=$?
    if [ $result -eq 0 ]; then
        echo "✓ $job_name 执行成功"
    else
        echo "✗ $job_name 执行失败 (错误码: $result)"
        echo "请检查错误信息并修复问题"
    fi
    
    return $result
}

# 执行所有任务
echo ""
echo "开始执行分析任务..."

# 第一部分：人口年龄分析
echo ""
echo "第一部分: RDD编程统计人口平均年龄"
run_spark_job "GeneratePeopleAge"
run_spark_job "CalculateAverageAge"

# 第二部分：咖啡数据分析
echo ""
echo "第二部分: 咖啡连锁店数据分析"
run_spark_job "CoffeeDataPreprocessing"
run_spark_job "CoffeeSalesRanking"
run_spark_job "CoffeeDistributionAnalysis"

echo ""
echo "=========================================="
echo "所有任务执行完成"
echo "=========================================="

# 显示结果
echo ""
echo "生成的结果目录:"
for dir in peopleage.txt average_age_result coffee_preprocessed_data coffee_sales_ranking coffee_distribution_analysis; do
    if [ -d "$dir" ]; then
        file_count=$(ls "$dir" 2>/dev/null | wc -l)
        echo "✓ $dir/ ($file_count 个文件)"
    else
        echo "✗ $dir/ (未生成)"
    fi
done

echo ""
echo "查看结果的命令:"
echo "cat average_age_result/part-00000"
echo "cat coffee_sales_ranking/part-00000"
echo "cat coffee_distribution_analysis/part-00000"

echo ""
echo "如果遇到问题，请检查:"
echo "1. Java和Spark是否正确安装"
echo "2. 内存是否足够（建议4GB以上）"
echo "3. 所有Scala文件是否在当前目录"
echo "4. CoffeeChain.csv数据文件是否存在"
