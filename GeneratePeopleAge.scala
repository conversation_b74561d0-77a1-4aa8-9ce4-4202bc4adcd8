import org.apache.spark.SparkContext
import org.apache.spark.SparkConf
import scala.util.Random
import java.io.PrintWriter

object GeneratePeopleAge {
  def main(args: Array[String]): Unit = {
    // 创建Spark配置
    val conf = new SparkConf().setAppName("GeneratePeopleAge").setMaster("local[*]")
    val sc = new SparkContext(conf)
    
    try {
      // 设置生成数据的行数
      val numRecords = 1000
      
      // 生成随机年龄数据 (18-90岁)
      val random = new Random()
      val ageData = (1 to numRecords).map { id =>
        val age = 18 + random.nextInt(73) // 18到90岁
        s"$id\t$age"
      }
      
      // 创建RDD
      val ageRDD = sc.parallelize(ageData)
      
      // 保存到文件
      val outputPath = "/home/<USER>/spark02/peopleage.txt"
      ageRDD.coalesce(1).saveAsTextFile(outputPath)
      
      println(s"成功生成 $numRecords 条人口年龄数据到文件: $outputPath")
      println("数据格式示例:")
      ageData.take(10).foreach(println)
      
    } catch {
      case e: Exception =>
        println(s"生成数据时发生错误: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      sc.stop()
    }
  }
}
