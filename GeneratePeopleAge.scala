import org.apache.spark.sql.SparkSession
import scala.util.Random

/**
 * 生成人口年龄数据文件
 * 功能：生成包含序号和年龄的数据文件peopleage.txt
 * 数据格式：序号 年龄（用制表符分隔）
 */
object GeneratePeopleAge {
  def main(args: Array[String]): Unit = {
    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("GeneratePeopleAge")
      .master("local[*]")
      .getOrCreate()
    
    val sc = spark.sparkContext
    
    // 设置日志级别
    sc.setLogLevel("WARN")
    
    println("开始生成人口年龄数据文件...")
    
    // 生成数据参数
    val numRecords = 1000  // 生成1000条记录
    val minAge = 18        // 最小年龄
    val maxAge = 80        // 最大年龄
    
    // 创建随机数生成器
    val random = new Random()
    
    // 生成数据：序号从1开始，年龄在18-80之间随机生成
    val peopleData = (1 to numRecords).map { id =>
      val age = minAge + random.nextInt(maxAge - minAge + 1)
      s"$id\t$age"
    }
    
    // 将数据转换为RDD
    val peopleRDD = sc.parallelize(peopleData)
    
    // 输出文件路径
    val outputPath = "peopleage.txt"
    
    // 保存数据到文件（先删除已存在的文件）
    try {
      // 删除已存在的输出目录
      val fs = org.apache.hadoop.fs.FileSystem.get(sc.hadoopConfiguration)
      val path = new org.apache.hadoop.fs.Path(outputPath)
      if (fs.exists(path)) {
        fs.delete(path, true)
        println(s"已删除存在的文件: $outputPath")
      }
    } catch {
      case _: Exception => // 忽略删除错误
    }
    
    // 保存RDD到文件
    peopleRDD.saveAsTextFile(outputPath)
    
    println(s"数据文件生成完成！")
    println(s"文件路径: $outputPath")
    println(s"记录数量: $numRecords")
    println(s"年龄范围: $minAge - $maxAge")
    
    // 显示前10条数据作为示例
    println("\n前10条数据示例：")
    println("序号\t年龄")
    println("----\t----")
    peopleRDD.take(10).foreach(println)
    
    // 统计信息
    val ages = peopleRDD.map(line => line.split("\t")(1).toInt)
    val totalAge = ages.sum()
    val avgAge = totalAge.toDouble / numRecords
    val minAgeActual = ages.min()
    val maxAgeActual = ages.max()
    
    println(s"\n数据统计信息：")
    println(s"总年龄: $totalAge")
    println(s"平均年龄: ${avgAge.formatted("%.2f")}")
    println(s"最小年龄: $minAgeActual")
    println(s"最大年龄: $maxAgeActual")
    
    // 关闭SparkSession
    spark.stop()
    
    println("\n程序执行完成！")
  }
}
