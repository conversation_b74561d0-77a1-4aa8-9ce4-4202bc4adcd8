#!/bin/bash

# 快速测试脚本 - 验证环境和运行简单测试

echo "=========================================="
echo "大数据实时处理技术 - 环境测试"
echo "=========================================="

# 检查当前目录
echo "当前工作目录: $(pwd)"
echo ""

# 检查必要文件
echo "检查必要文件..."
files=("CoffeeChain.csv" "GeneratePeopleAge.scala" "CalculateAverageAge.scala")
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file 存在"
    else
        echo "✗ $file 不存在"
    fi
done
echo ""

# 检查环境变量
echo "检查环境变量..."
echo "JAVA_HOME: ${JAVA_HOME:-未设置}"
echo "SPARK_HOME: ${SPARK_HOME:-未设置}"
echo "SCALA_HOME: ${SCALA_HOME:-未设置}"

# 自动检测Java路径
echo ""
echo "自动检测Java安装..."
java_paths=(
    "/usr/lib/jvm/java-1.8.0-openjdk"
    "/usr/lib/jvm/java-8-openjdk"
    "/usr/lib/jvm/java-1.8.0-openjdk-amd64"
    "/usr/lib/jvm/default-java"
    "/opt/java"
)

for path in "${java_paths[@]}"; do
    if [ -d "$path" ]; then
        echo "✓ 找到Java安装: $path"
        if [ -f "$path/bin/java" ]; then
            echo "  Java可执行文件存在"
            export JAVA_HOME="$path"
            break
        fi
    fi
done

if [ -z "$JAVA_HOME" ]; then
    echo "✗ 未找到Java安装，尝试使用系统Java"
    if command -v java >/dev/null 2>&1; then
        JAVA_PATH=$(which java)
        JAVA_HOME=$(dirname $(dirname $(readlink -f $JAVA_PATH)))
        echo "  系统Java路径: $JAVA_HOME"
        export JAVA_HOME="$JAVA_HOME"
    fi
fi

echo "最终JAVA_HOME: $JAVA_HOME"
echo ""

# 检查命令可用性
echo "检查命令可用性..."
commands=("java" "scala" "spark-submit")
for cmd in "${commands[@]}"; do
    if command -v "$cmd" >/dev/null 2>&1; then
        echo "✓ $cmd 可用"
        case $cmd in
            "java")
                java -version 2>&1 | head -1
                ;;
            "scala")
                scala -version 2>&1 | head -1
                ;;
            "spark-submit")
                spark-submit --version 2>&1 | grep "version" | head -1
                ;;
        esac
    else
        echo "✗ $cmd 不可用"
    fi
done
echo ""

# 检查CoffeeChain.csv文件内容
echo "检查数据文件..."
if [ -f "CoffeeChain.csv" ]; then
    echo "CoffeeChain.csv 文件信息:"
    echo "  文件大小: $(du -h CoffeeChain.csv | cut -f1)"
    echo "  行数: $(wc -l < CoffeeChain.csv)"
    echo "  表头: $(head -1 CoffeeChain.csv)"
    echo "  前3行数据:"
    head -4 CoffeeChain.csv | tail -3
else
    echo "✗ CoffeeChain.csv 文件不存在"
fi
echo ""

# 运行简单测试
echo "运行简单测试..."
echo "测试1: 生成少量人口年龄数据"

# 创建测试版本的GeneratePeopleAge
cat > TestGeneratePeopleAge.scala << 'EOF'
import org.apache.spark.SparkContext
import org.apache.spark.SparkConf
import scala.util.Random

object TestGeneratePeopleAge {
  def main(args: Array[String]): Unit = {
    val conf = new SparkConf().setAppName("TestGeneratePeopleAge").setMaster("local[*]")
    val sc = new SparkContext(conf)
    
    try {
      val numRecords = 10  // 只生成10条测试数据
      val random = new Random()
      val ageData = (1 to numRecords).map { id =>
        val age = 18 + random.nextInt(73)
        s"$id\t$age"
      }
      
      val ageRDD = sc.parallelize(ageData)
      ageRDD.coalesce(1).saveAsTextFile("test_peopleage")
      
      println(s"✓ 成功生成 $numRecords 条测试数据")
      println("测试数据内容:")
      ageData.foreach(println)
      
    } catch {
      case e: Exception =>
        println(s"✗ 测试失败: ${e.getMessage}")
    } finally {
      sc.stop()
    }
  }
}
EOF

echo "运行测试程序..."
if command -v spark-submit >/dev/null 2>&1; then
    spark-submit --class TestGeneratePeopleAge --master local[*] TestGeneratePeopleAge.scala
    
    if [ $? -eq 0 ]; then
        echo "✓ Spark测试成功"
        
        # 显示测试结果
        if [ -f "test_peopleage/part-00000" ]; then
            echo "生成的测试数据:"
            cat test_peopleage/part-00000
        fi
        
        # 清理测试文件
        rm -rf test_peopleage TestGeneratePeopleAge.scala
    else
        echo "✗ Spark测试失败"
    fi
else
    echo "✗ spark-submit 命令不可用，跳过测试"
fi

echo ""
echo "=========================================="
echo "环境测试完成"
echo "=========================================="

# 给出建议
echo ""
echo "建议："
echo "1. 如果所有检查都通过，可以运行: ./run_spark_analysis.sh"
echo "2. 如果环境有问题，请检查Java、Scala、Spark的安装和配置"
echo "3. 确保所有脚本都有执行权限: chmod +x *.sh"
echo "4. 如果在虚拟机中运行，确保有足够的内存（建议4GB以上）"
