#!/bin/bash

# 修复版本的Spark分析任务执行脚本
# 自动检测环境配置

echo "=========================================="
echo "大数据实时处理技术 - Spark分析任务 (修复版)"
echo "=========================================="

# 自动检测Java路径
detect_java() {
    echo "检测Java环境..."
    
    # 可能的Java路径
    java_paths=(
        "/usr/lib/jvm/java-1.8.0-openjdk"
        "/usr/lib/jvm/java-8-openjdk"
        "/usr/lib/jvm/java-1.8.0-openjdk-amd64"
        "/usr/lib/jvm/default-java"
        "/opt/java"
        "/usr/java/default"
    )
    
    # 首先检查环境变量
    if [ -n "$JAVA_HOME" ] && [ -f "$JAVA_HOME/bin/java" ]; then
        echo "✓ 使用已设置的JAVA_HOME: $JAVA_HOME"
        return 0
    fi
    
    # 检查预定义路径
    for path in "${java_paths[@]}"; do
        if [ -d "$path" ] && [ -f "$path/bin/java" ]; then
            export JAVA_HOME="$path"
            echo "✓ 找到Java安装: $JAVA_HOME"
            return 0
        fi
    done
    
    # 尝试从系统Java推导
    if command -v java >/dev/null 2>&1; then
        JAVA_PATH=$(which java)
        if [ -L "$JAVA_PATH" ]; then
            JAVA_HOME=$(dirname $(dirname $(readlink -f $JAVA_PATH)))
        else
            JAVA_HOME=$(dirname $(dirname $JAVA_PATH))
        fi
        
        if [ -f "$JAVA_HOME/bin/java" ]; then
            export JAVA_HOME="$JAVA_HOME"
            echo "✓ 从系统Java推导: $JAVA_HOME"
            return 0
        fi
    fi
    
    echo "✗ 未找到Java安装"
    return 1
}

# 检测Spark路径
detect_spark() {
    echo "检测Spark环境..."
    
    spark_paths=(
        "/opt/spark"
        "/usr/local/spark"
        "/home/<USER>"
        "$HOME/spark"
    )
    
    # 首先检查环境变量
    if [ -n "$SPARK_HOME" ] && [ -f "$SPARK_HOME/bin/spark-submit" ]; then
        echo "✓ 使用已设置的SPARK_HOME: $SPARK_HOME"
        return 0
    fi
    
    # 检查预定义路径
    for path in "${spark_paths[@]}"; do
        if [ -d "$path" ] && [ -f "$path/bin/spark-submit" ]; then
            export SPARK_HOME="$path"
            echo "✓ 找到Spark安装: $SPARK_HOME"
            return 0
        fi
    done
    
    # 尝试从系统命令推导
    if command -v spark-submit >/dev/null 2>&1; then
        SPARK_PATH=$(which spark-submit)
        SPARK_HOME=$(dirname $(dirname $SPARK_PATH))
        export SPARK_HOME="$SPARK_HOME"
        echo "✓ 从系统命令推导: $SPARK_HOME"
        return 0
    fi
    
    echo "✗ 未找到Spark安装"
    return 1
}

# 环境检测
echo "开始环境检测..."

if ! detect_java; then
    echo "错误: Java环境未正确配置"
    echo "请安装Java 8或设置正确的JAVA_HOME"
    exit 1
fi

if ! detect_spark; then
    echo "错误: Spark环境未正确配置"
    echo "请安装Spark或设置正确的SPARK_HOME"
    exit 1
fi

# 设置PATH
export PATH=$SPARK_HOME/bin:$JAVA_HOME/bin:$PATH

echo "环境配置:"
echo "  JAVA_HOME: $JAVA_HOME"
echo "  SPARK_HOME: $SPARK_HOME"
echo "  当前目录: $(pwd)"

# 验证Java版本
echo ""
echo "验证Java版本:"
$JAVA_HOME/bin/java -version

# 验证Spark版本
echo ""
echo "验证Spark版本:"
$SPARK_HOME/bin/spark-submit --version 2>&1 | head -3

# 检查必要文件
echo ""
echo "检查必要文件..."
required_files=("CoffeeChain.csv" "GeneratePeopleAge.scala" "CalculateAverageAge.scala")
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file 存在"
    else
        echo "✗ $file 不存在"
        echo "错误: 缺少必要文件 $file"
        exit 1
    fi
done

echo ""
echo "开始执行Spark分析任务..."

# 定义运行函数
run_spark_program() {
    local program_name=$1
    local scala_file="${program_name}.scala"
    
    echo ""
    echo "运行 $program_name..."
    echo "----------------------------------------"
    
    $SPARK_HOME/bin/spark-submit \
        --class "$program_name" \
        --master local[*] \
        --driver-memory 2g \
        --executor-memory 1g \
        --conf spark.sql.adaptive.enabled=false \
        --conf spark.serializer=org.apache.spark.serializer.KryoSerializer \
        "$scala_file"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        echo "✓ $program_name 执行成功"
        return 0
    else
        echo "✗ $program_name 执行失败 (退出码: $exit_code)"
        return 1
    fi
}

# 执行分析任务
echo ""
echo "第一部分: RDD编程统计人口平均年龄"
echo "=========================================="

echo "1. 生成模拟人口年龄数据..."
run_spark_program "GeneratePeopleAge"

echo ""
echo "2. 计算人口平均年龄..."
run_spark_program "CalculateAverageAge"

echo ""
echo "第二部分: 咖啡连锁店数据分析"
echo "=========================================="

echo "1. 数据预处理..."
run_spark_program "CoffeeDataPreprocessing"

echo ""
echo "2. 咖啡销售量排名分析..."
run_spark_program "CoffeeSalesRanking"

echo ""
echo "3. 咖啡销售分布分析..."
run_spark_program "CoffeeDistributionAnalysis"

echo ""
echo "=========================================="
echo "所有分析任务执行完成!"
echo "=========================================="

# 检查生成的结果文件
echo ""
echo "检查生成的结果文件:"
result_dirs=("peopleage.txt" "average_age_result" "coffee_preprocessed_data" "coffee_sales_ranking" "coffee_distribution_analysis")

for dir in "${result_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "✓ $dir/ - $(ls $dir | wc -l) 个文件"
    else
        echo "✗ $dir/ - 未生成"
    fi
done

echo ""
echo "使用以下命令查看结果:"
echo "cat average_age_result/part-00000"
echo "cat coffee_sales_ranking/part-00000"
echo "cat coffee_distribution_analysis/part-00000"
