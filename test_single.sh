#!/bin/bash

# 单个程序测试脚本
# 用法: ./test_single.sh [程序名]

if [ $# -eq 0 ]; then
    echo "用法: $0 <程序名>"
    echo ""
    echo "可用程序:"
    echo "  GeneratePeopleAge          - 生成人口年龄数据"
    echo "  CalculateAverageAge        - 计算平均年龄"
    echo "  CoffeeDataPreprocessing    - 咖啡数据预处理"
    echo "  CoffeeSalesRanking         - 销售排名分析"
    echo "  CoffeeDistributionAnalysis - 销售分布分析"
    echo ""
    echo "示例: ./test_single.sh GeneratePeopleAge"
    exit 1
fi

PROGRAM=$1
SCALA_FILE="${PROGRAM}.scala"

echo "=========================================="
echo "测试单个程序: $PROGRAM"
echo "=========================================="

# 检查文件是否存在
if [ ! -f "$SCALA_FILE" ]; then
    echo "错误: 文件 $SCALA_FILE 不存在"
    exit 1
fi

echo "✓ 找到文件: $SCALA_FILE"

# 检查Java环境
if [ -z "$JAVA_HOME" ]; then
    echo "警告: JAVA_HOME 未设置，尝试自动检测..."
    
    # 常见Java路径
    for java_path in /usr/lib/jvm/java-1.8.0-openjdk /usr/lib/jvm/java-8-openjdk; do
        if [ -d "$java_path" ] && [ -x "$java_path/bin/java" ]; then
            export JAVA_HOME="$java_path"
            echo "✓ 设置 JAVA_HOME: $JAVA_HOME"
            break
        fi
    done
    
    if [ -z "$JAVA_HOME" ]; then
        echo "错误: 无法找到Java安装"
        exit 1
    fi
fi

# 检查Spark环境
if [ -z "$SPARK_HOME" ]; then
    echo "警告: SPARK_HOME 未设置，尝试自动检测..."
    
    # 常见Spark路径
    for spark_path in /opt/spark /usr/local/spark; do
        if [ -d "$spark_path" ] && [ -x "$spark_path/bin/spark-submit" ]; then
            export SPARK_HOME="$spark_path"
            echo "✓ 设置 SPARK_HOME: $SPARK_HOME"
            break
        fi
    done
    
    if [ -z "$SPARK_HOME" ]; then
        echo "错误: 无法找到Spark安装"
        exit 1
    fi
fi

# 设置PATH
export PATH=$SPARK_HOME/bin:$JAVA_HOME/bin:$PATH

echo ""
echo "环境信息:"
echo "  JAVA_HOME: $JAVA_HOME"
echo "  SPARK_HOME: $SPARK_HOME"
echo "  当前目录: $(pwd)"

# 修改文件中的路径（如果需要）
echo ""
echo "修改文件路径..."
cp "$SCALA_FILE" "${SCALA_FILE}.backup"
sed -i 's|/home/<USER>/spark02/|./|g' "$SCALA_FILE"
echo "✓ 路径已修改为当前目录"

# 运行程序
echo ""
echo "运行程序..."
echo "命令: spark-submit --class $PROGRAM --master local[*] --driver-memory 1g $SCALA_FILE"
echo ""

$SPARK_HOME/bin/spark-submit \
    --class "$PROGRAM" \
    --master "local[*]" \
    --driver-memory 1g \
    --executor-memory 512m \
    --conf spark.sql.adaptive.enabled=false \
    "$SCALA_FILE"

RESULT=$?

# 恢复原文件
mv "${SCALA_FILE}.backup" "$SCALA_FILE"

echo ""
echo "=========================================="
if [ $RESULT -eq 0 ]; then
    echo "✓ 程序执行成功"
    
    # 显示可能生成的结果
    echo ""
    echo "检查生成的结果:"
    case $PROGRAM in
        "GeneratePeopleAge")
            if [ -d "peopleage.txt" ]; then
                echo "✓ 生成了 peopleage.txt 目录"
                echo "内容预览:"
                head -5 peopleage.txt/part-00000 2>/dev/null || echo "无法读取文件"
            fi
            ;;
        "CalculateAverageAge")
            if [ -d "average_age_result" ]; then
                echo "✓ 生成了 average_age_result 目录"
                echo "结果:"
                cat average_age_result/part-00000 2>/dev/null || echo "无法读取文件"
            fi
            ;;
        "CoffeeDataPreprocessing")
            if [ -d "coffee_preprocessed_data" ]; then
                echo "✓ 生成了 coffee_preprocessed_data 目录"
                echo "文件数量: $(ls coffee_preprocessed_data | wc -l)"
            fi
            ;;
        "CoffeeSalesRanking")
            if [ -d "coffee_sales_ranking" ]; then
                echo "✓ 生成了 coffee_sales_ranking 目录"
                echo "结果预览:"
                head -10 coffee_sales_ranking/part-00000 2>/dev/null || echo "无法读取文件"
            fi
            ;;
        "CoffeeDistributionAnalysis")
            if [ -d "coffee_distribution_analysis" ]; then
                echo "✓ 生成了 coffee_distribution_analysis 目录"
                echo "结果预览:"
                head -10 coffee_distribution_analysis/part-00000 2>/dev/null || echo "无法读取文件"
            fi
            ;;
    esac
else
    echo "✗ 程序执行失败 (错误码: $RESULT)"
    echo ""
    echo "可能的解决方案:"
    echo "1. 检查Java和Spark是否正确安装"
    echo "2. 确保有足够的内存"
    echo "3. 检查数据文件是否存在（对于咖啡分析程序需要CoffeeChain.csv）"
    echo "4. 查看上面的错误信息进行调试"
fi
echo "=========================================="
