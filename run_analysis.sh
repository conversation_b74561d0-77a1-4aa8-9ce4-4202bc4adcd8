#!/bin/bash

# 大数据实时处理技术 - 咖啡连锁店数据分析项目运行脚本
# 适用于CentOS环境

echo "========================================"
echo "大数据实时处理技术 - 数据分析项目"
echo "========================================"

# 检查环境
echo "检查运行环境..."

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境"
    exit 1
fi

# 检查Spark环境
if [ -z "$SPARK_HOME" ]; then
    echo "警告: SPARK_HOME环境变量未设置"
    echo "尝试使用默认路径: /opt/spark"
    export SPARK_HOME=/opt/spark
fi

if [ ! -d "$SPARK_HOME" ]; then
    echo "错误: Spark安装目录不存在: $SPARK_HOME"
    exit 1
fi

echo "Java版本: $(java -version 2>&1 | head -n 1)"
echo "Spark路径: $SPARK_HOME"

# 检查必要文件
echo "检查必要文件..."
if [ ! -f "CoffeeChain.csv" ]; then
    echo "错误: 未找到CoffeeChain.csv文件"
    exit 1
fi

required_files=(
    "GeneratePeopleAge.scala"
    "CalculateAverageAge.scala"
    "CoffeeDataPreprocessing.scala"
    "CoffeeSalesRanking.scala"
    "CoffeeSalesAnalysis.scala"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "错误: 未找到必要文件: $file"
        exit 1
    fi
done

echo "所有必要文件检查完成"

# 创建日志目录
mkdir -p logs

# 函数：运行Spark程序
run_spark_program() {
    local program_name=$1
    local description=$2

    echo ""
    echo "----------------------------------------"
    echo "运行: $description"
    echo "程序: $program_name"
    echo "----------------------------------------"

    # 记录开始时间
    start_time=$(date +%s)

    # 运行程序并记录日志
    $SPARK_HOME/bin/spark-shell \
        --master local[*] \
        --driver-memory 1g \
        --conf spark.sql.adaptive.enabled=true \
        -i "$program_name" \
        > "logs/${program_name%.scala}.log" 2>&1

    # 检查执行结果
    if [ $? -eq 0 ]; then
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        echo "✓ $description 完成 (耗时: ${duration}秒)"
    else
        echo "✗ $description 失败"
        echo "请查看日志文件: logs/${program_name%.scala}.log"
        echo "错误信息："
        tail -n 10 "logs/${program_name%.scala}.log"
        return 1
    fi
}

# 主执行流程
echo ""
echo "开始执行数据分析..."

# 第一部分：RDD编程统计人口平均年龄
echo ""
echo "========================================"
echo "第一部分：RDD编程统计人口平均年龄"
echo "========================================"

run_spark_program "GeneratePeopleAge.scala" "生成人口年龄数据"
if [ $? -ne 0 ]; then exit 1; fi

run_spark_program "CalculateAverageAge.scala" "计算平均年龄"
if [ $? -ne 0 ]; then exit 1; fi

# 第二部分：咖啡连锁店数据分析
echo ""
echo "========================================"
echo "第二部分：咖啡连锁店数据分析"
echo "========================================"

run_spark_program "CoffeeDataPreprocessing.scala" "数据预处理"
if [ $? -ne 0 ]; then exit 1; fi

run_spark_program "CoffeeSalesRanking.scala" "销售排名分析"
if [ $? -ne 0 ]; then exit 1; fi

run_spark_program "CoffeeSalesAnalysis.scala" "销售分布分析"
if [ $? -ne 0 ]; then exit 1; fi

# 生成执行报告
echo ""
echo "========================================"
echo "生成执行报告"
echo "========================================"

report_file="execution_report_$(date +%Y%m%d_%H%M%S).txt"

cat > "$report_file" << EOF
大数据实时处理技术 - 数据分析项目执行报告
==========================================

执行时间: $(date)
执行环境: CentOS
Java版本: $(java -version 2>&1 | head -n 1)
Spark路径: $SPARK_HOME

执行结果:
========

第一部分：RDD编程统计人口平均年龄
- ✓ 生成人口年龄数据
- ✓ 计算平均年龄

第二部分：咖啡连锁店数据分析
- ✓ 数据预处理
- ✓ 销售排名分析
- ✓ 销售分布分析

生成的输出文件:
==============

第一部分输出:
- peopleage.txt/ - 人口年龄数据
- average_age_result.txt/ - 平均年龄结果

第二部分输出:
- coffee_data_cleaned/ - 清洗后数据
- data_quality_report/ - 数据质量报告
- product_sales_ranking/ - 产品销售排名
- state_sales_ranking/ - 州销售排名
- comprehensive_sales_ranking/ - 综合销售排名
- sales_performance_analysis/ - 销售表现分析
- sales_ranking_report/ - 排名分析报告
- state_sales_analysis/ - 州销售关系分析
- market_sales_analysis/ - 市场销售关系分析
- profit_price_analysis/ - 利润售价分析
- profit_sales_relation/ - 利润销售关系分析
- cost_analysis/ - 成本关系分析
- attribute_analysis/ - 产品属性分析
- geo_market_analysis/ - 地域市场分析
- time_trend_analysis/ - 时间趋势分析
- comprehensive_sales_analysis_report/ - 综合分析报告

日志文件:
========
- logs/GeneratePeopleAge.log
- logs/CalculateAverageAge.log
- logs/CoffeeDataPreprocessing.log
- logs/CoffeeSalesRanking.log
- logs/CoffeeSalesAnalysis.log

项目状态: 全部完成 ✓
EOF

echo "执行报告已生成: $report_file"

# 显示输出文件统计
echo ""
echo "========================================"
echo "输出文件统计"
echo "========================================"

echo "生成的目录数量: $(find . -maxdepth 1 -type d -name "*sales*" -o -name "*age*" -o -name "*coffee*" -o -name "*analysis*" -o -name "*ranking*" | wc -l)"
echo "生成的文件总数: $(find . -maxdepth 2 -type f -name "part-*" | wc -l)"

# 显示一些关键结果
echo ""
echo "========================================"
echo "关键结果预览"
echo "========================================"

if [ -d "average_age_result.txt" ]; then
    echo "平均年龄计算结果:"
    head -n 10 average_age_result.txt/part-00000 2>/dev/null || echo "结果文件未找到"
fi

echo ""
echo "========================================"
echo "项目执行完成！"
echo "========================================"
echo ""
echo "下一步操作:"
echo "1. 查看执行报告: cat $report_file"
echo "2. 查看日志文件: ls logs/"
echo "3. 查看输出结果: ls -la *sales* *age* *analysis*"
echo "4. 填写实训报告: 实训报告模版.md"
echo ""
echo "如有问题，请查看相应的日志文件进行排查。"
