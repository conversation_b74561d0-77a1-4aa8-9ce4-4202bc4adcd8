#!/bin/bash

# 快速运行脚本 - 适用于CentOS环境
# 简化版本，适合快速测试

echo "大数据实时处理技术 - 快速运行"
echo "=============================="

# 设置Spark环境（如果未设置）
if [ -z "$SPARK_HOME" ]; then
    export SPARK_HOME=/opt/spark
fi

# 检查文件
if [ ! -f "CoffeeChain.csv" ]; then
    echo "错误: 未找到CoffeeChain.csv文件"
    exit 1
fi

echo "开始执行分析程序..."

# 第一部分
echo "1. 生成人口年龄数据..."
$SPARK_HOME/bin/spark-shell --master local[*] -i GeneratePeopleAge.scala > /dev/null 2>&1
echo "   完成"

echo "2. 计算平均年龄..."
$SPARK_HOME/bin/spark-shell --master local[*] -i CalculateAverageAge.scala > /dev/null 2>&1
echo "   完成"

# 第二部分
echo "3. 数据预处理..."
$SPARK_HOME/bin/spark-shell --master local[*] -i CoffeeDataPreprocessing.scala > /dev/null 2>&1
echo "   完成"

echo "4. 销售排名分析..."
$SPARK_HOME/bin/spark-shell --master local[*] -i CoffeeSalesRanking.scala > /dev/null 2>&1
echo "   完成"

echo "5. 销售分布分析..."
$SPARK_HOME/bin/spark-shell --master local[*] -i CoffeeSalesAnalysis.scala > /dev/null 2>&1
echo "   完成"

echo ""
echo "所有分析完成！"
echo "生成的文件："
ls -1 | grep -E "(age|sales|analysis|coffee|ranking)" | head -10
echo "..."
echo ""
echo "查看完整结果请运行: ls -la"
