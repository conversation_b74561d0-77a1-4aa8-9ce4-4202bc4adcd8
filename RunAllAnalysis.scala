import org.apache.spark.sql.SparkSession

/**
 * 运行所有分析程序的主程序
 * 功能：按顺序执行所有的数据分析任务
 */
object RunAllAnalysis {
  def main(args: Array[String]): Unit = {
    println("=" * 80)
    println("大数据实时处理技术 - 咖啡连锁店数据分析项目")
    println("=" * 80)
    
    val startTime = System.currentTimeMillis()
    
    try {
      // 第一部分：RDD编程统计人口平均年龄
      println("\n第一部分：RDD编程统计人口平均年龄")
      println("-" * 50)
      
      // 1. 生成人口年龄数据
      println("1. 生成人口年龄数据文件...")
      GeneratePeopleAge.main(Array())
      
      // 等待一下确保文件生成完成
      Thread.sleep(2000)
      
      // 2. 计算平均年龄
      println("\n2. 计算人口平均年龄...")
      CalculateAverageAge.main(Array())
      
      // 第二部分：咖啡连锁店数据分析
      println("\n\n第二部分：咖啡连锁店数据分析")
      println("-" * 50)
      
      // 3. 数据预处理
      println("3. 数据预处理...")
      CoffeeDataPreprocessing.main(Array())
      
      // 等待一下确保预处理完成
      Thread.sleep(3000)
      
      // 4. 销售量排名分析
      println("\n4. 销售量排名分析...")
      CoffeeSalesRanking.main(Array())
      
      // 等待一下确保排名分析完成
      Thread.sleep(2000)
      
      // 5. 销售分布分析
      println("\n5. 销售分布分析...")
      CoffeeSalesAnalysis.main(Array())
      
      val endTime = System.currentTimeMillis()
      val totalTime = (endTime - startTime) / 1000.0
      
      // 生成总结报告
      println("\n\n" + "=" * 80)
      println("所有分析任务完成！")
      println("=" * 80)
      
      println(s"总执行时间: ${totalTime.formatted("%.2f")} 秒")
      
      println("\n生成的文件列表：")
      println("第一部分 - RDD编程统计人口平均年龄：")
      println("  - peopleage.txt/ - 生成的人口年龄数据")
      println("  - average_age_result.txt/ - 平均年龄计算结果")
      
      println("\n第二部分 - 咖啡连锁店数据分析：")
      println("  - coffee_data_cleaned/ - 清洗后的数据")
      println("  - data_quality_report/ - 数据质量报告")
      println("  - product_sales_ranking/ - 产品销售排名")
      println("  - state_sales_ranking/ - 州销售排名")
      println("  - comprehensive_sales_ranking/ - 综合销售排名")
      println("  - sales_performance_analysis/ - 销售表现分析")
      println("  - sales_ranking_report/ - 排名分析报告")
      println("  - state_sales_analysis/ - 州销售关系分析")
      println("  - market_sales_analysis/ - 市场销售关系分析")
      println("  - profit_price_analysis/ - 利润售价分析")
      println("  - profit_sales_relation/ - 利润销售关系分析")
      println("  - cost_analysis/ - 成本关系分析")
      println("  - attribute_analysis/ - 产品属性分析")
      println("  - geo_market_analysis/ - 地域市场分析")
      println("  - time_trend_analysis/ - 时间趋势分析")
      println("  - comprehensive_sales_analysis_report/ - 综合分析报告")
      
      println("\n项目完成情况：")
      println("✓ 第一部分：RDD编程统计人口平均年龄 - 完成")
      println("✓ 第二部分：数据预处理 - 完成")
      println("✓ 第二部分：销售量排名分析 - 完成")
      println("✓ 第二部分：销售分布分析 - 完成")
      
      println("\n下一步：")
      println("1. 查看生成的分析结果文件")
      println("2. 根据分析结果填写实训报告")
      println("3. 准备演示和答辩材料")
      
    } catch {
      case e: Exception =>
        println(s"\n错误：执行过程中发生异常: ${e.getMessage}")
        e.printStackTrace()
        println("\n请检查错误信息并重新运行相应的程序")
    }
    
    println("\n" + "=" * 80)
  }
}
