// 大数据实时处理技术 - 咖啡连锁店数据分析项目
// 适用于Spark Shell环境的运行脚本

println("=" * 80)
println("大数据实时处理技术 - 咖啡连锁店数据分析项目")
println("=" * 80)
println("注意：请按顺序运行以下命令")
println("=" * 80)

println("\n第一部分：RDD编程统计人口平均年龄")
println("-" * 50)
println("请运行以下命令：")
println("1. :load GeneratePeopleAge.scala")
println("2. :load CalculateAverageAge.scala")

println("\n第二部分：咖啡连锁店数据分析")
println("-" * 50)
println("请运行以下命令：")
println("3. :load CoffeeDataPreprocessing.scala")
println("4. :load CoffeeSalesRanking.scala")
println("5. :load CoffeeSalesAnalysis.scala")

println("\n或者退出Spark Shell，使用以下方式运行：")
println("spark-shell < run_commands.scala")


