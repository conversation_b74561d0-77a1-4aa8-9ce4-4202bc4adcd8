# CentOS虚拟机中运行Spark分析任务说明

## 当前情况
您现在在Windows环境中，但需要在CentOS虚拟机中运行Spark程序。

## 操作步骤

### 1. 将文件传输到CentOS虚拟机

#### 方法一：使用共享文件夹
如果您的虚拟机配置了共享文件夹：
```bash
# 在CentOS虚拟机中
mkdir -p /home/<USER>/spark02
cp /mnt/shared/* /home/<USER>/spark02/
cd /home/<USER>/spark02
```

#### 方法二：使用SCP传输
如果虚拟机有网络连接：
```bash
# 在Windows中（如果有SSH客户端）
scp *.scala *.csv *.sh lbxx@虚拟机IP:/home/<USER>/spark02/

# 在CentOS虚拟机中
cd /home/<USER>/spark02
```

#### 方法三：手动复制
1. 在Windows中选择所有文件（Ctrl+A）
2. 复制（Ctrl+C）
3. 在虚拟机中创建目录并粘贴文件

### 2. 在CentOS虚拟机中执行

#### 步骤1：进入工作目录
```bash
cd /home/<USER>/spark02
ls -la  # 确认所有文件都在
```

#### 步骤2：给脚本添加执行权限
```bash
chmod +x *.sh
```

#### 步骤3：运行环境检测（推荐）
```bash
./setup_and_run.sh
```

这个脚本会：
- 自动检测Java和Spark环境
- 验证所有必要文件
- 按顺序执行所有分析任务
- 显示结果文件位置

#### 步骤4：如果环境检测失败，手动配置
```bash
# 查找Java安装位置
find /usr -name "java" -type f 2>/dev/null | grep bin

# 查找Spark安装位置
find /opt -name "spark-submit" -type f 2>/dev/null

# 设置环境变量（根据实际路径调整）
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
export SPARK_HOME=/opt/spark
export PATH=$SPARK_HOME/bin:$JAVA_HOME/bin:$PATH

# 验证配置
java -version
spark-submit --version
```

### 3. 单独运行程序（如果需要）

如果自动脚本有问题，可以单独运行每个程序：

```bash
# 第一部分：人口年龄分析
spark-submit --class GeneratePeopleAge --master local[*] GeneratePeopleAge.scala
spark-submit --class CalculateAverageAge --master local[*] CalculateAverageAge.scala

# 第二部分：咖啡数据分析
spark-submit --class CoffeeDataPreprocessing --master local[*] CoffeeDataPreprocessing.scala
spark-submit --class CoffeeSalesRanking --master local[*] CoffeeSalesRanking.scala
spark-submit --class CoffeeDistributionAnalysis --master local[*] CoffeeDistributionAnalysis.scala
```

### 4. 查看结果

```bash
# 查看人口平均年龄结果
cat average_age_result/part-00000

# 查看销售排名结果
cat coffee_sales_ranking/part-00000

# 查看分布分析结果
cat coffee_distribution_analysis/part-00000

# 查看所有生成的目录
ls -la | grep "^d"
```

## 常见问题解决

### 问题1：Java路径错误
```bash
# 查找正确的Java路径
sudo find / -name "java" -type f 2>/dev/null | grep bin | head -5

# 常见的Java路径
ls -la /usr/lib/jvm/
```

### 问题2：Spark未安装或路径错误
```bash
# 检查Spark是否安装
which spark-submit

# 如果未安装，下载Spark
wget https://downloads.apache.org/spark/spark-3.3.0/spark-3.3.0-bin-hadoop3.tgz
tar -xzf spark-3.3.0-bin-hadoop3.tgz
sudo mv spark-3.3.0-bin-hadoop3 /opt/spark
```

### 问题3：内存不足
```bash
# 减少内存使用
spark-submit --driver-memory 512m --executor-memory 256m --class 程序名 程序名.scala
```

### 问题4：权限问题
```bash
# 确保有读写权限
chmod 755 /home/<USER>/spark02
chmod 644 *.scala *.csv
chmod 755 *.sh
```

## 预期输出

成功运行后，您应该看到：

1. **生成的目录**：
   - `peopleage.txt/` - 人口年龄数据
   - `average_age_result/` - 平均年龄计算结果
   - `coffee_preprocessed_data/` - 预处理后的咖啡数据
   - `coffee_sales_ranking/` - 销售排名分析
   - `coffee_distribution_analysis/` - 分布分析结果

2. **控制台输出**：
   - 各种统计数据
   - 排名信息
   - 分析结果

## 如果仍有问题

1. **检查虚拟机配置**：
   - 内存至少4GB
   - 确保Java 8已安装
   - 确保Spark已正确配置

2. **查看详细错误信息**：
   ```bash
   # 运行时添加详细日志
   spark-submit --conf spark.sql.adaptive.enabled=false \
                --conf spark.ui.showConsoleProgress=true \
                --class 程序名 程序名.scala
   ```

3. **联系技术支持**：
   - 提供完整的错误信息
   - 说明虚拟机配置
   - 提供Java和Spark版本信息

## 备注

- 所有路径都已修改为使用当前目录，不再依赖特定的`/home/<USER>/spark02`路径
- 脚本会自动检测环境配置
- 如果自动检测失败，会提供手动配置的建议
- 所有程序都包含完整的错误处理和日志输出
