#!/bin/bash

# 大数据实时处理任务执行脚本
# 作者: 学生姓名
# 日期: $(date +%Y-%m-%d)

echo "=========================================="
echo "大数据实时处理技术 - Spark分析任务"
echo "=========================================="

# 设置环境变量
export SPARK_HOME=/opt/spark  # 根据实际安装路径调整
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk  # 根据实际Java路径调整
export PATH=$SPARK_HOME/bin:$PATH

# 设置工作目录
WORK_DIR="/home/<USER>/spark02"
cd $WORK_DIR

echo "当前工作目录: $(pwd)"
echo "开始执行Spark分析任务..."

# 检查必要文件是否存在
if [ ! -f "CoffeeChain.csv" ]; then
    echo "错误: CoffeeChain.csv 文件不存在!"
    exit 1
fi

echo ""
echo "第一部分: RDD编程统计人口平均年龄"
echo "=========================================="

echo "1. 生成模拟人口年龄数据..."
spark-submit --class GeneratePeopleAge \
    --master local[*] \
    --driver-memory 2g \
    --executor-memory 1g \
    GeneratePeopleAge.scala

if [ $? -eq 0 ]; then
    echo "✓ 人口年龄数据生成成功"
else
    echo "✗ 人口年龄数据生成失败"
fi

echo ""
echo "2. 计算人口平均年龄..."
spark-submit --class CalculateAverageAge \
    --master local[*] \
    --driver-memory 2g \
    --executor-memory 1g \
    CalculateAverageAge.scala

if [ $? -eq 0 ]; then
    echo "✓ 人口平均年龄计算成功"
else
    echo "✗ 人口平均年龄计算失败"
fi

echo ""
echo "第二部分: 咖啡连锁店数据分析"
echo "=========================================="

echo "1. 数据预处理..."
spark-submit --class CoffeeDataPreprocessing \
    --master local[*] \
    --driver-memory 2g \
    --executor-memory 1g \
    CoffeeDataPreprocessing.scala

if [ $? -eq 0 ]; then
    echo "✓ 数据预处理成功"
else
    echo "✗ 数据预处理失败"
fi

echo ""
echo "2. 咖啡销售量排名分析..."
spark-submit --class CoffeeSalesRanking \
    --master local[*] \
    --driver-memory 2g \
    --executor-memory 1g \
    CoffeeSalesRanking.scala

if [ $? -eq 0 ]; then
    echo "✓ 销售量排名分析成功"
else
    echo "✗ 销售量排名分析失败"
fi

echo ""
echo "3. 咖啡销售分布分析..."
spark-submit --class CoffeeDistributionAnalysis \
    --master local[*] \
    --driver-memory 2g \
    --executor-memory 1g \
    CoffeeDistributionAnalysis.scala

if [ $? -eq 0 ]; then
    echo "✓ 销售分布分析成功"
else
    echo "✗ 销售分布分析失败"
fi

echo ""
echo "=========================================="
echo "所有分析任务执行完成!"
echo "=========================================="

echo ""
echo "生成的结果文件:"
echo "- peopleage.txt/          : 模拟人口年龄数据"
echo "- average_age_result/     : 平均年龄计算结果"
echo "- coffee_preprocessed_data/: 预处理后的咖啡数据"
echo "- coffee_sales_ranking/   : 销售量排名分析结果"
echo "- coffee_distribution_analysis/: 销售分布分析结果"

echo ""
echo "请查看各个结果文件以获取详细分析报告。"
