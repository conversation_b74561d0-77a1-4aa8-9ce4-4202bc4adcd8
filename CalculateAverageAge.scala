import org.apache.spark.sql.SparkSession

/**
 * 计算人口平均年龄
 * 功能：读取peopleage.txt文件，使用RDD操作计算所有人口的平均年龄
 */
object CalculateAverageAge {
  def main(args: Array[String]): Unit = {
    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("CalculateAverageAge")
      .master("local[*]")
      .getOrCreate()
    
    val sc = spark.sparkContext
    
    // 设置日志级别
    sc.setLogLevel("WARN")
    
    println("开始计算人口平均年龄...")
    
    // 输入文件路径
    val inputPath = "peopleage.txt"
    
    try {
      // 读取数据文件
      val peopleRDD = sc.textFile(inputPath)
      
      // 检查文件是否为空
      if (peopleRDD.isEmpty()) {
        println(s"错误：文件 $inputPath 为空或不存在！")
        spark.stop()
        return
      }
      
      println(s"成功读取文件: $inputPath")
      
      // 获取总记录数
      val totalRecords = peopleRDD.count()
      println(s"总记录数: $totalRecords")
      
      // 显示前5条原始数据
      println("\n前5条原始数据：")
      println("序号\t年龄")
      println("----\t----")
      peopleRDD.take(5).foreach(println)
      
      // 数据处理：提取年龄字段
      // 每行格式：序号\t年龄
      val agesRDD = peopleRDD.map { line =>
        val parts = line.split("\t")
        if (parts.length >= 2) {
          try {
            parts(1).toInt  // 提取年龄（第二列）
          } catch {
            case _: NumberFormatException =>
              println(s"警告：无法解析年龄数据: $line")
              0  // 默认值
          }
        } else {
          println(s"警告：数据格式错误: $line")
          0  // 默认值
        }
      }.filter(_ > 0)  // 过滤掉无效数据
      
      // 验证处理后的数据
      val validRecords = agesRDD.count()
      println(s"有效记录数: $validRecords")
      
      if (validRecords == 0) {
        println("错误：没有有效的年龄数据！")
        spark.stop()
        return
      }
      
      // 方法1：使用reduce操作计算平均年龄
      println("\n=== 方法1：使用reduce操作 ===")
      val totalAge1 = agesRDD.reduce(_ + _)
      val averageAge1 = totalAge1.toDouble / validRecords
      println(s"总年龄: $totalAge1")
      println(s"平均年龄: ${averageAge1.formatted("%.2f")}")
      
      // 方法2：使用aggregate操作计算平均年龄
      println("\n=== 方法2：使用aggregate操作 ===")
      val (totalAge2, count2) = agesRDD.aggregate((0, 0))(
        (acc, age) => (acc._1 + age, acc._2 + 1),  // 分区内聚合
        (acc1, acc2) => (acc1._1 + acc2._1, acc1._2 + acc2._2)  // 分区间聚合
      )
      val averageAge2 = totalAge2.toDouble / count2
      println(s"总年龄: $totalAge2")
      println(s"记录数: $count2")
      println(s"平均年龄: ${averageAge2.formatted("%.2f")}")
      
      // 方法3：使用统计函数
      println("\n=== 方法3：使用统计函数 ===")
      val stats = agesRDD.stats()
      println(s"统计信息:")
      println(s"  记录数: ${stats.count}")
      println(s"  总和: ${stats.sum.toLong}")
      println(s"  平均值: ${stats.mean.formatted("%.2f")}")
      println(s"  最小值: ${stats.min.toInt}")
      println(s"  最大值: ${stats.max.toInt}")
      println(s"  标准差: ${stats.stdev.formatted("%.2f")}")
      
      // 年龄分布统计
      println("\n=== 年龄分布统计 ===")
      val ageGroups = agesRDD.map { age =>
        age match {
          case a if a < 30 => "18-29岁"
          case a if a < 40 => "30-39岁"
          case a if a < 50 => "40-49岁"
          case a if a < 60 => "50-59岁"
          case a if a < 70 => "60-69岁"
          case _ => "70岁以上"
        }
      }.countByValue()
      
      println("年龄段分布：")
      ageGroups.toSeq.sortBy(_._1).foreach { case (group, count) =>
        val percentage = (count.toDouble / validRecords * 100).formatted("%.1f")
        println(s"  $group: $count 人 ($percentage%)")
      }
      
      // 保存结果到文件
      val resultPath = "average_age_result.txt"
      val resultRDD = sc.parallelize(Seq(
        "人口平均年龄计算结果",
        "=" * 30,
        s"数据文件: $inputPath",
        s"总记录数: $validRecords",
        s"总年龄: $totalAge1",
        s"平均年龄: ${averageAge1.formatted("%.2f")} 岁",
        s"最小年龄: ${stats.min.toInt} 岁",
        s"最大年龄: ${stats.max.toInt} 岁",
        s"标准差: ${stats.stdev.formatted("%.2f")}",
        "",
        "年龄分布统计:",
        ageGroups.toSeq.sortBy(_._1).map { case (group, count) =>
          val percentage = (count.toDouble / validRecords * 100).formatted("%.1f")
          s"  $group: $count 人 ($percentage%)"
        }.mkString("\n")
      ))
      
      // 删除已存在的结果文件
      try {
        val fs = org.apache.hadoop.fs.FileSystem.get(sc.hadoopConfiguration)
        val path = new org.apache.hadoop.fs.Path(resultPath)
        if (fs.exists(path)) {
          fs.delete(path, true)
        }
      } catch {
        case _: Exception => // 忽略删除错误
      }
      
      resultRDD.saveAsTextFile(resultPath)
      println(s"\n结果已保存到文件: $resultPath")
      
    } catch {
      case e: Exception =>
        println(s"错误：处理文件时发生异常: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      // 关闭SparkSession
      spark.stop()
    }
    
    println("\n程序执行完成！")
  }
}
