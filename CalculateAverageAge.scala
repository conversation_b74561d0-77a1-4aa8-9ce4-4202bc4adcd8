import org.apache.spark.SparkContext
import org.apache.spark.SparkConf

object CalculateAverageAge {
  def main(args: Array[String]): Unit = {
    // 创建Spark配置
    val conf = new SparkConf().setAppName("CalculateAverageAge").setMaster("local[*]")
    val sc = new SparkContext(conf)
    
    try {
      // 读取人口年龄数据文件
      val inputPath = "/home/<USER>/spark02/peopleage.txt/part-00000"
      val ageRDD = sc.textFile(inputPath)
      
      println("开始计算人口平均年龄...")
      println(s"总记录数: ${ageRDD.count()}")
      
      // 解析数据，提取年龄字段
      val agesRDD = ageRDD.map { line =>
        val parts = line.split("\t")
        if (parts.length >= 2) {
          parts(1).toInt // 第二列是年龄
        } else {
          throw new IllegalArgumentException(s"数据格式错误: $line")
        }
      }
      
      // 缓存RDD以提高性能
      agesRDD.cache()
      
      // 计算总年龄和总人数
      val totalAge = agesRDD.reduce(_ + _)
      val totalCount = agesRDD.count()
      
      // 计算平均年龄
      val averageAge = totalAge.toDouble / totalCount
      
      // 输出结果
      println("=" * 50)
      println("人口年龄统计结果:")
      println("=" * 50)
      println(s"总人数: $totalCount")
      println(s"总年龄: $totalAge")
      println(s"平均年龄: %.2f 岁".format(averageAge))
      
      // 额外统计信息
      val maxAge = agesRDD.max()
      val minAge = agesRDD.min()
      
      println(s"最大年龄: $maxAge 岁")
      println(s"最小年龄: $minAge 岁")
      println("=" * 50)
      
      // 显示前10条数据作为验证
      println("前10条数据验证:")
      ageRDD.take(10).foreach(println)
      
      // 保存结果到文件
      val resultPath = "/home/<USER>/spark02/average_age_result"
      val resultRDD = sc.parallelize(Seq(
        "人口年龄统计结果",
        "=" * 30,
        s"总人数: $totalCount",
        s"总年龄: $totalAge",
        s"平均年龄: %.2f 岁".format(averageAge),
        s"最大年龄: $maxAge 岁",
        s"最小年龄: $minAge 岁"
      ))
      
      resultRDD.coalesce(1).saveAsTextFile(resultPath)
      println(s"结果已保存到: $resultPath")
      
    } catch {
      case e: Exception =>
        println(s"计算平均年龄时发生错误: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      sc.stop()
    }
  }
}
