import org.apache.spark.SparkContext
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession

object CoffeeDataPreprocessing {
  def main(args: Array[String]): Unit = {
    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("CoffeeDataPreprocessing")
      .master("local[*]")
      .getOrCreate()
    
    val sc = spark.sparkContext
    
    try {
      // 读取咖啡连锁店数据
      val inputPath = "/home/<USER>/spark02/CoffeeChain.csv"
      val rawData = sc.textFile(inputPath)
      
      // 获取表头
      val header = rawData.first()
      println("数据表头:")
      println(header)
      println("=" * 80)
      
      // 过滤掉表头，获取数据行
      val dataLines = rawData.filter(line => line != header)
      
      println(s"原始数据总行数: ${rawData.count()}")
      println(s"数据行数(不含表头): ${dataLines.count()}")
      
      // 解析CSV数据
      val parsedData = dataLines.map { line =>
        // 处理CSV中可能包含逗号的字段（用引号包围）
        val fields = parseCSVLine(line)
        
        // 提取关键字段
        CoffeeRecord(
          areaCode = fields(0),
          date = fields(1),
          market = fields(2),
          marketSize = fields(3),
          product = fields(4),
          productType = fields(5),
          state = fields(6),
          coffeeType = fields(7),
          budgetCogs = safeToDouble(fields(8)),
          budgetMargin = safeToDouble(fields(9)),
          budgetProfit = safeToDouble(fields(10)),
          budgetSales = safeToDouble(fields(11)),
          coffeeSales = safeToDouble(fields(12)),
          cogs = safeToDouble(fields(13)),
          inventory = safeToDouble(fields(14).replace(",", "")), // 移除千位分隔符
          margin = safeToDouble(fields(15)),
          marketing = safeToDouble(fields(16)),
          numberOfRecords = safeToInt(fields(17)),
          profit = safeToDouble(fields(18)),
          totalExpenses = safeToDouble(fields(19))
        )
      }
      
      // 缓存处理后的数据
      parsedData.cache()
      
      // 数据质量检查
      println("数据预处理完成，进行数据质量检查...")
      
      val validRecords = parsedData.filter(_.isValid)
      val invalidRecords = parsedData.filter(!_.isValid)
      
      println(s"有效记录数: ${validRecords.count()}")
      println(s"无效记录数: ${invalidRecords.count()}")
      
      // 显示数据统计信息
      println("\n数据统计信息:")
      println("=" * 50)
      
      // 按州统计
      val stateStats = validRecords.map(r => (r.state, 1)).reduceByKey(_ + _).collect().sortBy(-_._2)
      println("各州记录数统计:")
      stateStats.foreach { case (state, count) => 
        println(s"  $state: $count 条记录")
      }
      
      // 按产品类型统计
      val productTypeStats = validRecords.map(r => (r.productType, 1)).reduceByKey(_ + _).collect().sortBy(-_._2)
      println("\n产品类型统计:")
      productTypeStats.foreach { case (productType, count) => 
        println(s"  $productType: $count 条记录")
      }
      
      // 按市场规模统计
      val marketSizeStats = validRecords.map(r => (r.marketSize, 1)).reduceByKey(_ + _).collect().sortBy(-_._2)
      println("\n市场规模统计:")
      marketSizeStats.foreach { case (marketSize, count) => 
        println(s"  $marketSize: $count 条记录")
      }
      
      // 保存预处理后的数据
      val outputPath = "/home/<USER>/spark02/coffee_preprocessed_data"
      validRecords.map(_.toCSVString).coalesce(1).saveAsTextFile(outputPath)
      
      println(s"\n预处理后的数据已保存到: $outputPath")
      
      // 显示前5条记录作为示例
      println("\n前5条预处理后的数据示例:")
      validRecords.take(5).foreach(record => println(record.toString))
      
    } catch {
      case e: Exception =>
        println(s"数据预处理时发生错误: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      spark.stop()
    }
  }
  
  // 解析CSV行，处理可能包含逗号的字段
  def parseCSVLine(line: String): Array[String] = {
    val fields = scala.collection.mutable.ArrayBuffer[String]()
    var current = new StringBuilder()
    var inQuotes = false
    var i = 0
    
    while (i < line.length) {
      val char = line.charAt(i)
      
      if (char == '"') {
        inQuotes = !inQuotes
      } else if (char == ',' && !inQuotes) {
        fields += current.toString().trim
        current = new StringBuilder()
      } else {
        current += char
      }
      i += 1
    }
    
    fields += current.toString().trim
    fields.toArray
  }
  
  // 安全转换为Double
  def safeToDouble(str: String): Double = {
    try {
      str.replace(",", "").toDouble
    } catch {
      case _: NumberFormatException => 0.0
    }
  }
  
  // 安全转换为Int
  def safeToInt(str: String): Int = {
    try {
      str.replace(",", "").toInt
    } catch {
      case _: NumberFormatException => 0
    }
  }
}

// 咖啡记录案例类
case class CoffeeRecord(
  areaCode: String,
  date: String,
  market: String,
  marketSize: String,
  product: String,
  productType: String,
  state: String,
  coffeeType: String,
  budgetCogs: Double,
  budgetMargin: Double,
  budgetProfit: Double,
  budgetSales: Double,
  coffeeSales: Double,
  cogs: Double,
  inventory: Double,
  margin: Double,
  marketing: Double,
  numberOfRecords: Int,
  profit: Double,
  totalExpenses: Double
) {
  // 检查记录是否有效
  def isValid: Boolean = {
    areaCode.nonEmpty && state.nonEmpty && product.nonEmpty && 
    coffeeSales >= 0 && profit != Double.NaN && margin != Double.NaN
  }
  
  // 转换为CSV字符串
  def toCSVString: String = {
    s"$areaCode,$date,$market,$marketSize,$product,$productType,$state,$coffeeType," +
    s"$budgetCogs,$budgetMargin,$budgetProfit,$budgetSales,$coffeeSales,$cogs," +
    s"$inventory,$margin,$marketing,$numberOfRecords,$profit,$totalExpenses"
  }
}
