import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._

/**
 * 咖啡连锁店数据预处理
 * 功能：读取CoffeeChain.csv文件，进行数据清洗和预处理
 */
object CoffeeDataPreprocessing {
  def main(args: Array[String]): Unit = {
    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("CoffeeDataPreprocessing")
      .master("local[*]")
      .getOrCreate()
    
    import spark.implicits._
    
    // 设置日志级别
    spark.sparkContext.setLogLevel("WARN")
    
    println("开始咖啡连锁店数据预处理...")
    
    // 输入文件路径
    val inputPath = "CoffeeChain.csv"
    
    try {
      // 读取CSV文件
      val rawDF = spark.read
        .option("header", "true")
        .option("inferSchema", "true")
        .csv(inputPath)
      
      println(s"成功读取文件: $inputPath")
      println(s"原始数据行数: ${rawDF.count()}")
      println(s"原始数据列数: ${rawDF.columns.length}")
      
      // 显示数据结构
      println("\n数据结构：")
      rawDF.printSchema()
      
      // 显示前5行数据
      println("\n前5行原始数据：")
      rawDF.show(5, truncate = false)
      
      // 检查列名
      println("\n列名列表：")
      rawDF.columns.zipWithIndex.foreach { case (col, idx) =>
        println(s"$idx: $col")
      }
      
      // 数据清洗
      println("\n开始数据清洗...")
      
      // 1. 处理缺失值
      println("1. 检查缺失值...")
      rawDF.columns.foreach { col =>
        val nullCount = rawDF.filter(rawDF(col).isNull || rawDF(col) === "").count()
        if (nullCount > 0) {
          println(s"  列 '$col' 有 $nullCount 个缺失值")
        }
      }
      
      // 2. 数据类型转换和清洗
      println("2. 数据类型转换...")
      val cleanedDF = rawDF
        .withColumn("Coffee Sales", 
          when(col("Coffee Sales").isNull, 0.0)
          .otherwise(col("Coffee Sales").cast(DoubleType)))
        .withColumn("Profit", 
          when(col("Profit").isNull, 0.0)
          .otherwise(col("Profit").cast(DoubleType)))
        .withColumn("Margin", 
          when(col("Margin").isNull, 0.0)
          .otherwise(col("Margin").cast(DoubleType)))
        .withColumn("Budget Sales", 
          when(col("Budget Sales").isNull, 0.0)
          .otherwise(col("Budget Sales").cast(DoubleType)))
        .withColumn("Budget Profit", 
          when(col("Budget Profit").isNull, 0.0)
          .otherwise(col("Budget Profit").cast(DoubleType)))
        .withColumn("Total Expenses", 
          when(col("Total Expenses").isNull, 0.0)
          .otherwise(col("Total Expenses").cast(DoubleType)))
        .withColumn("Marketing", 
          when(col("Marketing").isNull, 0.0)
          .otherwise(col("Marketing").cast(DoubleType)))
        .withColumn("Cogs", 
          when(col("Cogs").isNull, 0.0)
          .otherwise(col("Cogs").cast(DoubleType)))
        .filter(col("Coffee Sales") >= 0)  // 过滤掉负销售额
        .filter(col("State").isNotNull && col("State") =!= "")  // 过滤掉空的州名
        .filter(col("Product").isNotNull && col("Product") =!= "")  // 过滤掉空的产品名
      
      println(s"清洗后数据行数: ${cleanedDF.count()}")
      
      // 3. 添加计算字段
      println("3. 添加计算字段...")
      val enrichedDF = cleanedDF
        .withColumn("Profit_Margin_Ratio", 
          when(col("Coffee Sales") > 0, col("Profit") / col("Coffee Sales"))
          .otherwise(0.0))
        .withColumn("Sales_vs_Budget_Ratio", 
          when(col("Budget Sales") > 0, col("Coffee Sales") / col("Budget Sales"))
          .otherwise(0.0))
        .withColumn("Date_Year", year(to_date(col("Ddate"), "M/d/yy")))
        .withColumn("Date_Month", month(to_date(col("Ddate"), "M/d/yy")))
      
      // 4. 数据质量检查
      println("4. 数据质量检查...")
      
      // 检查异常值
      val salesStats = enrichedDF.select("Coffee Sales").describe()
      println("\n咖啡销售额统计：")
      salesStats.show()
      
      val profitStats = enrichedDF.select("Profit").describe()
      println("\n利润统计：")
      profitStats.show()
      
      // 检查重复记录
      val totalRows = enrichedDF.count()
      val distinctRows = enrichedDF.distinct().count()
      println(s"\n重复记录检查：")
      println(s"总行数: $totalRows")
      println(s"去重后行数: $distinctRows")
      println(s"重复记录数: ${totalRows - distinctRows}")
      
      // 5. 基本统计信息
      println("\n5. 基本统计信息...")
      
      // 按州统计
      println("\n各州记录数统计：")
      enrichedDF.groupBy("State")
        .count()
        .orderBy(desc("count"))
        .show(10)
      
      // 按产品类型统计
      println("\n各产品类型记录数统计：")
      enrichedDF.groupBy("Product Type")
        .count()
        .orderBy(desc("count"))
        .show()
      
      // 按市场规模统计
      println("\n各市场规模记录数统计：")
      enrichedDF.groupBy("Market Size")
        .count()
        .orderBy(desc("count"))
        .show()
      
      // 6. 保存清洗后的数据
      println("6. 保存清洗后的数据...")
      val outputPath = "coffee_data_cleaned"
      
      // 删除已存在的输出目录
      try {
        val fs = org.apache.hadoop.fs.FileSystem.get(spark.sparkContext.hadoopConfiguration)
        val path = new org.apache.hadoop.fs.Path(outputPath)
        if (fs.exists(path)) {
          fs.delete(path, true)
          println(s"已删除存在的目录: $outputPath")
        }
      } catch {
        case _: Exception => // 忽略删除错误
      }
      
      // 保存为Parquet格式（更高效）
      enrichedDF.write
        .mode("overwrite")
        .option("header", "true")
        .parquet(outputPath)
      
      println(s"清洗后的数据已保存到: $outputPath")
      
      // 7. 生成数据质量报告
      val reportPath = "data_quality_report.txt"
      val report = Seq(
        "咖啡连锁店数据预处理报告",
        "=" * 40,
        s"原始数据文件: $inputPath",
        s"原始数据行数: ${rawDF.count()}",
        s"原始数据列数: ${rawDF.columns.length}",
        s"清洗后数据行数: ${enrichedDF.count()}",
        s"数据清洗率: ${((enrichedDF.count().toDouble / rawDF.count()) * 100).formatted("%.2f")}%",
        s"重复记录数: ${totalRows - distinctRows}",
        "",
        "主要清洗操作:",
        "- 处理缺失值",
        "- 数据类型转换",
        "- 过滤负销售额记录",
        "- 过滤空值记录",
        "- 添加计算字段",
        "",
        "输出文件: " + outputPath
      )
      
      spark.sparkContext.parallelize(report).saveAsTextFile(reportPath.replace(".txt", ""))
      println(s"数据质量报告已保存到: $reportPath")
      
    } catch {
      case e: Exception =>
        println(s"错误：处理文件时发生异常: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      // 关闭SparkSession
      spark.stop()
    }
    
    println("\n数据预处理完成！")
  }
}
