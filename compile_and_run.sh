#!/bin/bash

# 编译和运行Spark Scala程序的脚本
# 使用方法: ./compile_and_run.sh [程序名]

echo "=========================================="
echo "Spark Scala 程序编译和运行脚本"
echo "=========================================="

# 检查参数
if [ $# -eq 0 ]; then
    echo "使用方法: $0 <程序名>"
    echo "可用程序:"
    echo "  GeneratePeopleAge"
    echo "  CalculateAverageAge"
    echo "  CoffeeDataPreprocessing"
    echo "  CoffeeSalesRanking"
    echo "  CoffeeDistributionAnalysis"
    echo "  all  (运行所有程序)"
    exit 1
fi

# 设置环境变量
export SPARK_HOME=${SPARK_HOME:-/opt/spark}
export JAVA_HOME=${JAVA_HOME:-/usr/lib/jvm/java-8-openjdk}
export SCALA_HOME=${SCALA_HOME:-/opt/scala}

# 检查环境
echo "检查环境..."
if [ ! -d "$SPARK_HOME" ]; then
    echo "错误: SPARK_HOME 目录不存在: $SPARK_HOME"
    exit 1
fi

if [ ! -d "$JAVA_HOME" ]; then
    echo "错误: JAVA_HOME 目录不存在: $JAVA_HOME"
    exit 1
fi

echo "✓ Spark Home: $SPARK_HOME"
echo "✓ Java Home: $JAVA_HOME"

# 设置类路径
SPARK_JARS="$SPARK_HOME/jars/*"
SCALA_LIBRARY="$SCALA_HOME/lib/scala-library.jar"

# 编译函数
compile_scala() {
    local program=$1
    local scala_file="${program}.scala"
    
    echo "编译 $scala_file ..."
    
    if [ ! -f "$scala_file" ]; then
        echo "错误: 文件 $scala_file 不存在"
        return 1
    fi
    
    # 创建临时目录
    mkdir -p classes
    
    # 编译Scala文件
    scalac -cp "$SPARK_JARS" -d classes "$scala_file"
    
    if [ $? -eq 0 ]; then
        echo "✓ $scala_file 编译成功"
        
        # 创建JAR文件
        cd classes
        jar cf "../${program}.jar" *.class
        cd ..
        
        echo "✓ 创建 ${program}.jar 成功"
        return 0
    else
        echo "✗ $scala_file 编译失败"
        return 1
    fi
}

# 运行函数
run_spark() {
    local program=$1
    local jar_file="${program}.jar"
    
    echo "运行 $program ..."
    
    if [ ! -f "$jar_file" ]; then
        echo "错误: JAR文件 $jar_file 不存在，请先编译"
        return 1
    fi
    
    spark-submit \
        --class "$program" \
        --master local[*] \
        --driver-memory 2g \
        --executor-memory 1g \
        "$jar_file"
    
    if [ $? -eq 0 ]; then
        echo "✓ $program 运行成功"
        return 0
    else
        echo "✗ $program 运行失败"
        return 1
    fi
}

# 直接运行Scala文件（不编译JAR）
run_scala_direct() {
    local program=$1
    local scala_file="${program}.scala"
    
    echo "直接运行 $scala_file ..."
    
    if [ ! -f "$scala_file" ]; then
        echo "错误: 文件 $scala_file 不存在"
        return 1
    fi
    
    spark-submit \
        --class "$program" \
        --master local[*] \
        --driver-memory 2g \
        --executor-memory 1g \
        "$scala_file"
    
    if [ $? -eq 0 ]; then
        echo "✓ $program 运行成功"
        return 0
    else
        echo "✗ $program 运行失败"
        return 1
    fi
}

# 主程序
PROGRAM=$1

case $PROGRAM in
    "GeneratePeopleAge")
        echo "执行第一部分：生成人口年龄数据"
        run_scala_direct "$PROGRAM"
        ;;
    "CalculateAverageAge")
        echo "执行第一部分：计算平均年龄"
        run_scala_direct "$PROGRAM"
        ;;
    "CoffeeDataPreprocessing")
        echo "执行第二部分：咖啡数据预处理"
        run_scala_direct "$PROGRAM"
        ;;
    "CoffeeSalesRanking")
        echo "执行第二部分：销售量排名分析"
        run_scala_direct "$PROGRAM"
        ;;
    "CoffeeDistributionAnalysis")
        echo "执行第二部分：销售分布分析"
        run_scala_direct "$PROGRAM"
        ;;
    "all")
        echo "执行所有程序..."
        echo ""
        
        echo "第一部分：RDD编程统计人口平均年龄"
        echo "=========================================="
        run_scala_direct "GeneratePeopleAge"
        echo ""
        run_scala_direct "CalculateAverageAge"
        echo ""
        
        echo "第二部分：咖啡连锁店数据分析"
        echo "=========================================="
        run_scala_direct "CoffeeDataPreprocessing"
        echo ""
        run_scala_direct "CoffeeSalesRanking"
        echo ""
        run_scala_direct "CoffeeDistributionAnalysis"
        echo ""
        
        echo "所有程序执行完成！"
        ;;
    *)
        echo "错误: 未知程序 '$PROGRAM'"
        echo "可用程序: GeneratePeopleAge, CalculateAverageAge, CoffeeDataPreprocessing, CoffeeSalesRanking, CoffeeDistributionAnalysis, all"
        exit 1
        ;;
esac

echo ""
echo "=========================================="
echo "执行完成！"
echo "=========================================="

# 显示生成的文件
echo ""
echo "生成的结果文件:"
ls -la | grep -E "(peopleage|average_age|coffee_)" | grep "^d"
