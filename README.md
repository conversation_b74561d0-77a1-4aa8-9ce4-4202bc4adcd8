# 大数据实时处理技术 - 咖啡连锁店数据分析项目

## 项目概述

本项目是《大数据实时处理技术》课程的期末考试项目，包含两个主要部分：
1. 使用RDD编程统计人口平均年龄
2. 基于咖啡连锁店的Spark数据处理分析

## 环境要求

- **操作系统**: CentOS 7
- **Java**: OpenJDK 1.8.0
- **Scala**: 2.12.x
- **Spark**: 3.x
- **工作目录**: /home/<USER>/spark02/

## 项目结构

```
.
├── README.md                           # 项目说明文档
├── 任务要求.md                         # 任务要求说明
├── 实训报告模版.md                     # 完整的实训报告
├── CoffeeChain.csv                     # 咖啡连锁店原始数据
├── GeneratePeopleAge.scala             # 生成人口年龄数据程序
├── CalculateAverageAge.scala           # 计算平均年龄程序
├── CoffeeDataPreprocessing.scala       # 咖啡数据预处理程序
├── CoffeeSalesRanking.scala            # 咖啡销售排名分析程序
├── CoffeeSalesAnalysis.scala           # 咖啡销售分布分析程序
└── RunAllAnalysis.scala                # 运行所有分析的主程序
```

## 运行说明

### 方法一：运行所有程序（推荐）

```bash
# 进入工作目录
cd /home/<USER>/spark02/

# 启动Spark Shell并运行主程序
spark-shell -i RunAllAnalysis.scala
```

### 方法二：分步运行

#### 第一部分：RDD编程统计人口平均年龄

```bash
# 1. 生成人口年龄数据
spark-shell -i GeneratePeopleAge.scala

# 2. 计算平均年龄
spark-shell -i CalculateAverageAge.scala
```

#### 第二部分：咖啡连锁店数据分析

```bash
# 3. 数据预处理
spark-shell -i CoffeeDataPreprocessing.scala

# 4. 销售排名分析
spark-shell -i CoffeeSalesRanking.scala

# 5. 销售分布分析
spark-shell -i CoffeeSalesAnalysis.scala
```

### 方法三：使用spark-submit运行

```bash
# 编译Scala文件（如果需要）
scalac -cp "$SPARK_HOME/jars/*" *.scala

# 运行单个程序
spark-submit --class GeneratePeopleAge --master local[*] GeneratePeopleAge.scala
```

## 输出文件说明

### 第一部分输出文件

- `peopleage.txt/` - 生成的人口年龄数据文件
- `average_age_result.txt/` - 平均年龄计算结果

### 第二部分输出文件

#### 数据预处理
- `coffee_data_cleaned/` - 清洗后的咖啡数据（Parquet格式）
- `data_quality_report/` - 数据质量报告

#### 销售排名分析
- `product_sales_ranking/` - 产品销售排名
- `state_sales_ranking/` - 州销售排名
- `comprehensive_sales_ranking/` - 综合销售排名
- `sales_performance_analysis/` - 销售表现分析
- `sales_ranking_report/` - 排名分析报告

#### 销售分布分析
- `state_sales_analysis/` - 州销售关系分析
- `market_sales_analysis/` - 市场销售关系分析
- `profit_price_analysis/` - 利润售价分析
- `profit_sales_relation/` - 利润销售关系分析
- `cost_analysis/` - 成本关系分析
- `attribute_analysis/` - 产品属性分析
- `geo_market_analysis/` - 地域市场分析
- `time_trend_analysis/` - 时间趋势分析
- `comprehensive_sales_analysis_report/` - 综合分析报告

## 程序功能说明

### GeneratePeopleAge.scala
- 生成1000条人口年龄记录
- 年龄范围：18-80岁
- 输出格式：序号\t年龄
- 提供基本统计信息

### CalculateAverageAge.scala
- 读取peopleage.txt文件
- 使用三种方法计算平均年龄：
  - reduce操作
  - aggregate操作
  - 统计函数
- 提供年龄分布分析

### CoffeeDataPreprocessing.scala
- 读取CoffeeChain.csv文件
- 数据清洗和类型转换
- 处理缺失值和异常值
- 添加计算字段
- 生成数据质量报告

### CoffeeSalesRanking.scala
- 按产品销售量排名
- 按州销售量排名
- 按市场规模排名
- 按产品类型排名
- 销售表现分析

### CoffeeSalesAnalysis.scala
- 咖啡销售量和State的关系分析
- 咖啡销售量和Market的关系分析
- 平均利润和售价关系分析
- 成本结构分析
- 产品属性综合分析
- 地域市场关系分析
- 时间趋势分析

### RunAllAnalysis.scala
- 按顺序执行所有分析程序
- 提供执行进度提示
- 生成完整的文件列表
- 计算总执行时间

## 注意事项

1. **文件路径**: 确保CoffeeChain.csv文件在当前工作目录下
2. **内存配置**: 如果数据量大，可能需要调整Spark内存配置
3. **输出覆盖**: 程序会自动删除已存在的输出目录
4. **编码格式**: 确保CSV文件使用UTF-8编码
5. **权限问题**: 确保对工作目录有读写权限

## 故障排除

### 常见问题

1. **内存不足**
   ```bash
   # 增加driver内存
   spark-shell --driver-memory 2g -i RunAllAnalysis.scala
   ```

2. **文件编码问题**
   ```bash
   # 转换文件编码
   iconv -f GBK -t UTF-8 CoffeeChain.csv > CoffeeChain_utf8.csv
   ```

3. **权限问题**
   ```bash
   # 修改文件权限
   chmod 755 /home/<USER>/spark02/
   ```

4. **Spark环境问题**
   ```bash
   # 检查Spark环境
   echo $SPARK_HOME
   spark-shell --version
   ```

## 联系信息

- 学生姓名: lbxx
- 学号: 202200001
- 专业: 大数据技术
- 班级: 2022级（本）

## 项目完成情况

- [x] 第一部分：RDD编程统计人口平均年龄
- [x] 第二部分：数据预处理
- [x] 第二部分：销售量排名分析
- [x] 第二部分：销售分布分析
- [x] 实训报告编写
- [x] 代码注释和文档

项目已全部完成，可以直接运行和演示。
