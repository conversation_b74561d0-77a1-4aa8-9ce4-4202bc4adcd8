# 大数据实时处理技术 - Spark分析项目

## 项目概述

本项目是《大数据实时处理技术》课程的期末考试项目，包含两个主要部分：
1. 使用RDD编程统计人口平均年龄
2. 基于咖啡连锁店的Spark数据处理分析

## 环境要求

### 硬件环境
- 操作系统：CentOS 7.x
- 内存：4GB以上
- 硬盘：50GB以上

### 软件环境
- Java 8 (OpenJDK)
- Scala 2.12.x
- Apache Spark 3.x
- Hadoop (可选)

## 文件结构

```
lbxx2/
├── CoffeeChain.csv                    # 咖啡连锁店原始数据
├── GeneratePeopleAge.scala            # 生成人口年龄数据
├── CalculateAverageAge.scala          # 计算平均年龄
├── CoffeeDataPreprocessing.scala      # 咖啡数据预处理
├── CoffeeSalesRanking.scala           # 销售量排名分析
├── CoffeeDistributionAnalysis.scala   # 销售分布分析
├── run_spark_analysis.sh              # 执行脚本
├── 任务要求.md                        # 任务要求说明
├── 实训报告模版.md                    # 完整实训报告
└── README.md                          # 本文件
```

## 安装和配置

### 1. 环境变量设置

```bash
# 设置Java环境
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk

# 设置Spark环境
export SPARK_HOME=/opt/spark
export PATH=$SPARK_HOME/bin:$PATH

# 设置Scala环境
export SCALA_HOME=/opt/scala
export PATH=$SCALA_HOME/bin:$PATH
```

### 2. 验证环境

```bash
# 检查Java版本
java -version

# 检查Scala版本
scala -version

# 检查Spark版本
spark-submit --version
```

## 使用方法

### 方法一：使用执行脚本（推荐）

1. 将所有文件复制到 `/home/<USER>/spark02/` 目录
2. 给脚本添加执行权限：
   ```bash
   chmod +x run_spark_analysis.sh
   ```
3. 执行脚本：
   ```bash
   ./run_spark_analysis.sh
   ```

### 方法二：手动执行

#### 第一部分：人口年龄分析

1. 生成模拟数据：
   ```bash
   spark-submit --class GeneratePeopleAge \
       --master local[*] \
       --driver-memory 2g \
       GeneratePeopleAge.scala
   ```

2. 计算平均年龄：
   ```bash
   spark-submit --class CalculateAverageAge \
       --master local[*] \
       --driver-memory 2g \
       CalculateAverageAge.scala
   ```

#### 第二部分：咖啡数据分析

1. 数据预处理：
   ```bash
   spark-submit --class CoffeeDataPreprocessing \
       --master local[*] \
       --driver-memory 2g \
       CoffeeDataPreprocessing.scala
   ```

2. 销售量排名分析：
   ```bash
   spark-submit --class CoffeeSalesRanking \
       --master local[*] \
       --driver-memory 2g \
       CoffeeSalesRanking.scala
   ```

3. 销售分布分析：
   ```bash
   spark-submit --class CoffeeDistributionAnalysis \
       --master local[*] \
       --driver-memory 2g \
       CoffeeDistributionAnalysis.scala
   ```

## 输出结果

执行完成后，将在工作目录下生成以下结果文件：

- `peopleage.txt/` - 模拟人口年龄数据
- `average_age_result/` - 平均年龄计算结果
- `coffee_preprocessed_data/` - 预处理后的咖啡数据
- `coffee_sales_ranking/` - 销售量排名分析结果
- `coffee_distribution_analysis/` - 销售分布分析结果

## 查看结果

```bash
# 查看人口年龄数据
cat peopleage.txt/part-00000 | head -10

# 查看平均年龄结果
cat average_age_result/part-00000

# 查看销售排名结果
cat coffee_sales_ranking/part-00000

# 查看分布分析结果
cat coffee_distribution_analysis/part-00000
```

## 常见问题

### 1. 内存不足错误
**解决方案**：增加内存配置
```bash
spark-submit --driver-memory 4g --executor-memory 2g ...
```

### 2. 文件路径错误
**解决方案**：确保所有文件都在正确的路径下，使用绝对路径

### 3. 权限问题
**解决方案**：确保有读写权限
```bash
chmod 755 /home/<USER>/spark02/
```

### 4. Spark版本兼容性
**解决方案**：确保使用Spark 3.x版本，如果使用其他版本可能需要调整代码

## 代码说明

### 核心技术点

1. **RDD操作**：
   - `map()` - 数据转换
   - `filter()` - 数据过滤
   - `reduceByKey()` - 按键聚合
   - `sortBy()` - 排序
   - `cache()` - 缓存

2. **数据处理**：
   - CSV文件解析
   - 数据类型转换
   - 异常处理
   - 数据清洗

3. **分析方法**：
   - 统计分析
   - 排名分析
   - 多维度分析
   - 关联分析

## 学习目标

通过本项目，学生将掌握：

1. Spark RDD编程模型
2. 大数据处理的基本流程
3. 数据预处理和清洗技术
4. 多维度数据分析方法
5. 实际业务问题的解决思路

## 扩展练习

1. 尝试使用Spark SQL重写分析逻辑
2. 添加数据可视化功能
3. 实现实时数据处理
4. 集成机器学习算法

## 联系方式

如有问题，请联系：
- 课程老师
- 实验室助教

## 版权说明

本项目仅用于教学目的，请勿用于商业用途。
