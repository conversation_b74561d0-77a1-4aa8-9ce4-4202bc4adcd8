import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._

/**
 * 咖啡销售分布分析
 * 功能：全面分析咖啡销售量的分布情况，包括各种维度的关系分析
 */
object CoffeeSalesAnalysis {
  def main(args: Array[String]): Unit = {
    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("CoffeeSalesAnalysis")
      .master("local[*]")
      .getOrCreate()
    
    import spark.implicits._
    
    // 设置日志级别
    spark.sparkContext.setLogLevel("WARN")
    
    println("开始咖啡销售分布分析...")
    
    try {
      // 读取清洗后的数据
      val inputPath = "coffee_data_cleaned"
      val coffeeDF = spark.read.parquet(inputPath)
      
      println(s"成功读取数据: $inputPath")
      println(s"数据行数: ${coffeeDF.count()}")
      
      // 1. 咖啡销售量和State的关系
      println("\n=== 1. 咖啡销售量和State的关系 ===")
      val stateAnalysis = coffeeDF
        .groupBy("State")
        .agg(
          sum("Coffee Sales").alias("Total_Sales"),
          avg("Coffee Sales").alias("Avg_Sales"),
          min("Coffee Sales").alias("Min_Sales"),
          max("Coffee Sales").alias("Max_Sales"),
          stddev("Coffee Sales").alias("Sales_StdDev"),
          count("*").alias("Record_Count")
        )
        .withColumn("Sales_CV", col("Sales_StdDev") / col("Avg_Sales"))  // 变异系数
        .orderBy(desc("Total_Sales"))
      
      println("各州销售量分析：")
      stateAnalysis.show(truncate = false)
      
      // 保存州销售关系分析
      val stateAnalysisPath = "state_sales_analysis"
      stateAnalysis.write.mode("overwrite").option("header", "true").csv(stateAnalysisPath)
      
      // 2. 咖啡销售量和Market的关系
      println("\n=== 2. 咖啡销售量和Market的关系 ===")
      val marketAnalysis = coffeeDF
        .groupBy("Market", "Market Size")
        .agg(
          sum("Coffee Sales").alias("Total_Sales"),
          avg("Coffee Sales").alias("Avg_Sales"),
          min("Coffee Sales").alias("Min_Sales"),
          max("Coffee Sales").alias("Max_Sales"),
          count("*").alias("Record_Count"),
          countDistinct("State").alias("State_Count"),
          countDistinct("Product").alias("Product_Count")
        )
        .orderBy(desc("Total_Sales"))
      
      println("各市场销售量分析：")
      marketAnalysis.show(truncate = false)
      
      // 保存市场销售关系分析
      val marketAnalysisPath = "market_sales_analysis"
      marketAnalysis.write.mode("overwrite").option("header", "true").csv(marketAnalysisPath)
      
      // 3. 咖啡的平均利润和售价
      println("\n=== 3. 咖啡的平均利润和售价 ===")
      val profitPriceAnalysis = coffeeDF
        .groupBy("Product")
        .agg(
          avg("Coffee Sales").alias("Avg_Price"),
          avg("Profit").alias("Avg_Profit"),
          avg("Margin").alias("Avg_Margin"),
          sum("Coffee Sales").alias("Total_Sales"),
          sum("Profit").alias("Total_Profit"),
          count("*").alias("Record_Count")
        )
        .withColumn("Profit_Margin_Percent", 
          when(col("Avg_Price") > 0, col("Avg_Profit") / col("Avg_Price") * 100)
          .otherwise(0))
        .orderBy(desc("Avg_Profit"))
      
      println("产品平均利润和售价分析：")
      profitPriceAnalysis.show(20, truncate = false)
      
      // 保存利润售价分析
      val profitPriceAnalysisPath = "profit_price_analysis"
      profitPriceAnalysis.write.mode("overwrite").option("header", "true").csv(profitPriceAnalysisPath)
      
      // 4. 咖啡的平均利润、售价和销售量的关系
      println("\n=== 4. 平均利润、售价和销售量的关系 ===")
      val profitSalesRelation = coffeeDF
        .groupBy("Product")
        .agg(
          avg("Coffee Sales").alias("Avg_Sales"),
          avg("Profit").alias("Avg_Profit"),
          sum("Coffee Sales").alias("Total_Sales"),
          sum("Profit").alias("Total_Profit"),
          count("*").alias("Sales_Frequency")
        )
        .withColumn("Profit_Per_Sale", col("Total_Profit") / col("Sales_Frequency"))
        .withColumn("Sales_Efficiency", col("Total_Sales") / col("Sales_Frequency"))
        .orderBy(desc("Total_Sales"))
      
      println("利润、售价和销售量关系分析：")
      profitSalesRelation.show(20, truncate = false)
      
      // 保存关系分析
      val profitSalesRelationPath = "profit_sales_relation"
      profitSalesRelation.write.mode("overwrite").option("header", "true").csv(profitSalesRelationPath)
      
      // 5. 咖啡的平均利润、销售量与其他成本的关系
      println("\n=== 5. 平均利润、销售量与其他成本的关系 ===")
      val costAnalysis = coffeeDF
        .groupBy("Product")
        .agg(
          avg("Coffee Sales").alias("Avg_Sales"),
          avg("Profit").alias("Avg_Profit"),
          avg("Cogs").alias("Avg_COGS"),
          avg("Marketing").alias("Avg_Marketing"),
          avg("Total Expenses").alias("Avg_Total_Expenses"),
          sum("Coffee Sales").alias("Total_Sales"),
          sum("Profit").alias("Total_Profit")
        )
        .withColumn("COGS_Ratio", col("Avg_COGS") / col("Avg_Sales"))
        .withColumn("Marketing_Ratio", col("Avg_Marketing") / col("Avg_Sales"))
        .withColumn("Expense_Ratio", col("Avg_Total_Expenses") / col("Avg_Sales"))
        .withColumn("Net_Profit_Margin", 
          (col("Avg_Sales") - col("Avg_Total_Expenses")) / col("Avg_Sales"))
        .orderBy(desc("Net_Profit_Margin"))
      
      println("成本关系分析：")
      costAnalysis.show(20, truncate = false)
      
      // 保存成本分析
      val costAnalysisPath = "cost_analysis"
      costAnalysis.write.mode("overwrite").option("header", "true").csv(costAnalysisPath)
      
      // 6. 咖啡属性与平均售价、平均利润、销售量与其他成本的关系
      println("\n=== 6. 咖啡属性综合分析 ===")
      val attributeAnalysis = coffeeDF
        .groupBy("Product Type", "Type")
        .agg(
          avg("Coffee Sales").alias("Avg_Sales"),
          avg("Profit").alias("Avg_Profit"),
          avg("Margin").alias("Avg_Margin"),
          avg("Cogs").alias("Avg_COGS"),
          avg("Marketing").alias("Avg_Marketing"),
          sum("Coffee Sales").alias("Total_Sales"),
          sum("Profit").alias("Total_Profit"),
          count("*").alias("Record_Count"),
          countDistinct("Product").alias("Product_Variety")
        )
        .withColumn("Profit_Margin_Percent", 
          when(col("Avg_Sales") > 0, col("Avg_Profit") / col("Avg_Sales") * 100)
          .otherwise(0))
        .orderBy(desc("Total_Sales"))
      
      println("咖啡属性综合分析：")
      attributeAnalysis.show(truncate = false)
      
      // 保存属性分析
      val attributeAnalysisPath = "attribute_analysis"
      attributeAnalysis.write.mode("overwrite").option("header", "true").csv(attributeAnalysisPath)
      
      // 7. 市场规模、市场地域与销售量的关系
      println("\n=== 7. 市场规模、市场地域与销售量的关系 ===")
      val geoMarketAnalysis = coffeeDF
        .groupBy("Market", "Market Size", "State")
        .agg(
          sum("Coffee Sales").alias("Total_Sales"),
          avg("Coffee Sales").alias("Avg_Sales"),
          sum("Profit").alias("Total_Profit"),
          avg("Profit").alias("Avg_Profit"),
          count("*").alias("Record_Count"),
          countDistinct("Product").alias("Product_Count")
        )
        .withColumn("Sales_Per_Product", col("Total_Sales") / col("Product_Count"))
        .orderBy(desc("Total_Sales"))
      
      println("市场地域销售关系分析：")
      geoMarketAnalysis.show(30, truncate = false)
      
      // 保存地域市场分析
      val geoMarketAnalysisPath = "geo_market_analysis"
      geoMarketAnalysis.write.mode("overwrite").option("header", "true").csv(geoMarketAnalysisPath)

      // 8. 时间趋势分析
      println("\n=== 8. 时间趋势分析 ===")
      val timeAnalysis = coffeeDF
        .groupBy("Date_Year", "Date_Month")
        .agg(
          sum("Coffee Sales").alias("Monthly_Sales"),
          avg("Coffee Sales").alias("Avg_Monthly_Sales"),
          sum("Profit").alias("Monthly_Profit"),
          count("*").alias("Record_Count")
        )
        .orderBy("Date_Year", "Date_Month")

      println("月度销售趋势：")
      timeAnalysis.show(24, truncate = false)

      // 保存时间分析
      val timeAnalysisPath = "time_trend_analysis"
      timeAnalysis.write.mode("overwrite").option("header", "true").csv(timeAnalysisPath)

      // 9. 生成综合分析报告
      println("\n=== 9. 生成综合分析报告 ===")

      // 获取关键统计数据
      val totalSales = coffeeDF.agg(sum("Coffee Sales")).collect()(0).getDouble(0)
      val totalProfit = coffeeDF.agg(sum("Profit")).collect()(0).getDouble(0)
      val avgProfitMargin = (totalProfit / totalSales * 100)

      val topState = stateAnalysis.first()
      val topProduct = profitPriceAnalysis.first()
      val topMarket = marketAnalysis.first()

      val reportData = Seq(
        "咖啡销售分布分析综合报告",
        "=" * 60,
        "",
        "一、总体概况",
        s"总销售额: ${totalSales.formatted("%.2f")}",
        s"总利润: ${totalProfit.formatted("%.2f")}",
        s"整体利润率: ${avgProfitMargin.formatted("%.2f")}%",
        s"分析记录数: ${coffeeDF.count()}",
        "",
        "二、关键发现",
        s"1. 销售额最高的州: ${topState.getAs[String]("State")} (${topState.getAs[Double]("Total_Sales").formatted("%.2f")})",
        s"2. 利润最高的产品: ${topProduct.getAs[String]("Product")} (${topProduct.getAs[Double]("Avg_Profit").formatted("%.2f")})",
        s"3. 销售额最高的市场: ${topMarket.getAs[String]("Market")} (${topMarket.getAs[Double]("Total_Sales").formatted("%.2f")})",
        "",
        "三、分析维度",
        "1. 州销售关系分析 -> state_sales_analysis",
        "2. 市场销售关系分析 -> market_sales_analysis",
        "3. 利润售价分析 -> profit_price_analysis",
        "4. 利润销售关系分析 -> profit_sales_relation",
        "5. 成本关系分析 -> cost_analysis",
        "6. 产品属性分析 -> attribute_analysis",
        "7. 地域市场分析 -> geo_market_analysis",
        "8. 时间趋势分析 -> time_trend_analysis",
        "",
        "四、结论",
        "- 不同州的销售表现存在显著差异",
        "- 产品类型对利润率有重要影响",
        "- 市场规模与销售量呈正相关",
        "- 成本控制是提高利润的关键因素",
        "- 地域因素对销售策略制定具有指导意义"
      )

      val reportPath = "comprehensive_sales_analysis_report"
      spark.sparkContext.parallelize(reportData).saveAsTextFile(reportPath)
      println(s"综合分析报告已保存到: $reportPath")

      // 10. 输出所有分析结果文件列表
      println("\n=== 10. 分析结果文件列表 ===")
      val outputFiles = Seq(
        "state_sales_analysis - 州销售关系分析",
        "market_sales_analysis - 市场销售关系分析",
        "profit_price_analysis - 利润售价分析",
        "profit_sales_relation - 利润销售关系分析",
        "cost_analysis - 成本关系分析",
        "attribute_analysis - 产品属性分析",
        "geo_market_analysis - 地域市场分析",
        "time_trend_analysis - 时间趋势分析",
        "comprehensive_sales_analysis_report - 综合分析报告"
      )

      println("已生成的分析结果文件：")
      outputFiles.foreach(file => println(s"  - $file"))

    } catch {
      case e: Exception =>
        println(s"错误：分析过程中发生异常: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      // 关闭SparkSession
      spark.stop()
    }

    println("\n咖啡销售分布分析完成！")
  }
}
