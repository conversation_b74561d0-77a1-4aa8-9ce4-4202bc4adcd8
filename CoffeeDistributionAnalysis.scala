import org.apache.spark.SparkContext
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession

object CoffeeDistributionAnalysis {
  def main(args: Array[String]): Unit = {
    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("CoffeeDistributionAnalysis")
      .master("local[*]")
      .getOrCreate()
    
    val sc = spark.sparkContext
    
    try {
      // 读取咖啡连锁店数据
      val inputPath = "/home/<USER>/spark02/CoffeeChain.csv"
      val rawData = sc.textFile(inputPath)
      
      // 获取表头并过滤
      val header = rawData.first()
      val dataLines = rawData.filter(line => line != header)
      
      println("开始咖啡销售分布分析...")
      println("=" * 80)
      
      // 解析数据
      val coffeeData = dataLines.map { line =>
        val fields = parseCSVLine(line)
        CoffeeRecord(
          product = fields(4),
          productType = fields(5),
          state = fields(6),
          market = fields(2),
          marketSize = fields(3),
          coffeeSales = safeToDouble(fields(12)),
          profit = safeToDouble(fields(18)),
          budgetSales = safeToDouble(fields(11)),
          margin = safeToDouble(fields(15)),
          totalExpenses = safeToDouble(fields(19))
        )
      }.filter(_.coffeeSales > 0)
      
      coffeeData.cache()
      
      println(s"有效记录数: ${coffeeData.count()}")
      
      // 1. 咖啡销售量和State的关系分析
      println("\n1. 咖啡销售量和州(State)的关系分析:")
      println("-" * 60)
      
      val stateAnalysis = coffeeData
        .map(r => (r.state, (r.coffeeSales, r.profit, 1)))
        .reduceByKey { case ((sales1, profit1, count1), (sales2, profit2, count2)) =>
          (sales1 + sales2, profit1 + profit2, count1 + count2)
        }
        .map { case (state, (totalSales, totalProfit, count)) =>
          (state, totalSales, totalProfit, count, totalSales / count, totalProfit / count)
        }
        .sortBy(-_._2) // 按总销售量排序
      
      val stateResults = stateAnalysis.collect()
      println(f"${"州"}%-12s ${"总销售量"}%12s ${"总利润"}%12s ${"记录数"}%8s ${"平均销售"}%12s ${"平均利润"}%12s")
      println("-" * 80)
      stateResults.foreach { case (state, totalSales, totalProfit, count, avgSales, avgProfit) =>
        println(f"$state%-12s ${totalSales}%12.2f ${totalProfit}%12.2f $count%8d ${avgSales}%12.2f ${avgProfit}%12.2f")
      }
      
      // 2. 咖啡销售量和Market的关系分析
      println("\n2. 咖啡销售量和市场(Market)的关系分析:")
      println("-" * 60)
      
      val marketAnalysis = coffeeData
        .map(r => (r.market, (r.coffeeSales, r.profit, 1)))
        .reduceByKey { case ((sales1, profit1, count1), (sales2, profit2, count2)) =>
          (sales1 + sales2, profit1 + profit2, count1 + count2)
        }
        .map { case (market, (totalSales, totalProfit, count)) =>
          (market, totalSales, totalProfit, count, totalSales / count, totalProfit / count)
        }
        .sortBy(-_._2)
      
      val marketResults = marketAnalysis.collect()
      println(f"${"市场"}%-15s ${"总销售量"}%12s ${"总利润"}%12s ${"记录数"}%8s ${"平均销售"}%12s ${"平均利润"}%12s")
      println("-" * 80)
      marketResults.foreach { case (market, totalSales, totalProfit, count, avgSales, avgProfit) =>
        println(f"$market%-15s ${totalSales}%12.2f ${totalProfit}%12.2f $count%8d ${avgSales}%12.2f ${avgProfit}%12.2f")
      }
      
      // 3. 咖啡的平均利润和售价分析
      println("\n3. 咖啡的平均利润和售价分析:")
      println("-" * 60)
      
      val profitSalesAnalysis = coffeeData
        .map(r => (r.product, (r.profit, r.budgetSales, r.coffeeSales, 1)))
        .reduceByKey { case ((profit1, budgetSales1, sales1, count1), (profit2, budgetSales2, sales2, count2)) =>
          (profit1 + profit2, budgetSales1 + budgetSales2, sales1 + sales2, count1 + count2)
        }
        .map { case (product, (totalProfit, totalBudgetSales, totalSales, count)) =>
          (product, totalProfit / count, totalBudgetSales / count, totalSales / count, totalProfit / totalSales)
        }
        .sortBy(-_._2) // 按平均利润排序
        .take(10)
      
      println(f"${"产品"}%-25s ${"平均利润"}%12s ${"平均预算售价"}%15s ${"平均实际销售"}%15s ${"利润率"}%10s")
      println("-" * 80)
      profitSalesAnalysis.foreach { case (product, avgProfit, avgBudgetSales, avgSales, profitMargin) =>
        println(f"$product%-25s ${avgProfit}%12.2f ${avgBudgetSales}%15.2f ${avgSales}%15.2f ${profitMargin * 100}%9.2f%%")
      }
      
      // 4. 咖啡的平均利润、销售量与其他成本的关系
      println("\n4. 咖啡平均利润、销售量与成本的关系分析:")
      println("-" * 80)
      
      val costAnalysis = coffeeData
        .map(r => (r.productType, (r.profit, r.coffeeSales, r.totalExpenses, r.margin, 1)))
        .reduceByKey { case ((profit1, sales1, expenses1, margin1, count1), (profit2, sales2, expenses2, margin2, count2)) =>
          (profit1 + profit2, sales1 + sales2, expenses1 + expenses2, margin1 + margin2, count1 + count2)
        }
        .map { case (productType, (totalProfit, totalSales, totalExpenses, totalMargin, count)) =>
          val avgProfit = totalProfit / count
          val avgSales = totalSales / count
          val avgExpenses = totalExpenses / count
          val avgMargin = totalMargin / count
          val costEfficiency = if (avgExpenses > 0) avgProfit / avgExpenses else 0
          (productType, avgProfit, avgSales, avgExpenses, avgMargin, costEfficiency)
        }
        .sortBy(-_._6) // 按成本效率排序
      
      val costResults = costAnalysis.collect()
      println(f"${"产品类型"}%-15s ${"平均利润"}%12s ${"平均销售"}%12s ${"平均成本"}%12s ${"平均毛利"}%12s ${"成本效率"}%12s")
      println("-" * 80)
      costResults.foreach { case (productType, avgProfit, avgSales, avgExpenses, avgMargin, costEfficiency) =>
        println(f"$productType%-15s ${avgProfit}%12.2f ${avgSales}%12.2f ${avgExpenses}%12.2f ${avgMargin}%12.2f ${costEfficiency}%12.4f")
      }
      
      // 5. 市场规模、市场地域与销售量的关系
      println("\n5. 市场规模、市场地域与销售量的关系分析:")
      println("-" * 80)
      
      val marketRegionAnalysis = coffeeData
        .map(r => ((r.marketSize, r.market), (r.coffeeSales, r.profit, 1)))
        .reduceByKey { case ((sales1, profit1, count1), (sales2, profit2, count2)) =>
          (sales1 + sales2, profit1 + profit2, count1 + count2)
        }
        .map { case ((marketSize, market), (totalSales, totalProfit, count)) =>
          (marketSize, market, totalSales, totalProfit, count, totalSales / count, totalProfit / count)
        }
        .sortBy(-_._3) // 按总销售量排序
      
      val marketRegionResults = marketRegionAnalysis.collect()
      println(f"${"市场规模"}%-15s ${"市场地域"}%-15s ${"总销售量"}%12s ${"总利润"}%12s ${"记录数"}%8s ${"平均销售"}%12s")
      println("-" * 90)
      marketRegionResults.foreach { case (marketSize, market, totalSales, totalProfit, count, avgSales, avgProfit) =>
        println(f"$marketSize%-15s $market%-15s ${totalSales}%12.2f ${totalProfit}%12.2f $count%8d ${avgSales}%12.2f")
      }
      
      // 保存分析结果
      val outputPath = "/home/<USER>/spark02/coffee_distribution_analysis"
      
      val analysisResults = sc.parallelize(
        Seq("咖啡销售分布分析报告", "=" * 50, "") ++
        Seq("1. 咖啡销售量和州的关系分析:", "-" * 30) ++
        stateResults.map { case (state, totalSales, totalProfit, count, avgSales, avgProfit) =>
          f"$state: 总销售=${totalSales}%.2f, 平均销售=${avgSales}%.2f, 总利润=${totalProfit}%.2f"
        } ++
        Seq("", "2. 咖啡销售量和市场的关系分析:", "-" * 30) ++
        marketResults.map { case (market, totalSales, totalProfit, count, avgSales, avgProfit) =>
          f"$market: 总销售=${totalSales}%.2f, 平均销售=${avgSales}%.2f, 总利润=${totalProfit}%.2f"
        } ++
        Seq("", "3. 产品平均利润和售价分析:", "-" * 30) ++
        profitSalesAnalysis.map { case (product, avgProfit, avgBudgetSales, avgSales, profitMargin) =>
          f"$product: 平均利润=${avgProfit}%.2f, 平均售价=${avgBudgetSales}%.2f, 利润率=${profitMargin * 100}%.2f%%"
        }
      )
      
      analysisResults.coalesce(1).saveAsTextFile(outputPath)
      
      println(s"\n分析结果已保存到: $outputPath")
      
    } catch {
      case e: Exception =>
        println(s"分布分析时发生错误: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      spark.stop()
    }
  }
  
  // 解析CSV行
  def parseCSVLine(line: String): Array[String] = {
    val fields = scala.collection.mutable.ArrayBuffer[String]()
    var current = new StringBuilder()
    var inQuotes = false
    var i = 0
    
    while (i < line.length) {
      val char = line.charAt(i)
      
      if (char == '"') {
        inQuotes = !inQuotes
      } else if (char == ',' && !inQuotes) {
        fields += current.toString().trim
        current = new StringBuilder()
      } else {
        current += char
      }
      i += 1
    }
    
    fields += current.toString().trim
    fields.toArray
  }
  
  // 安全转换为Double
  def safeToDouble(str: String): Double = {
    try {
      str.replace(",", "").replace("\"", "").toDouble
    } catch {
      case _: NumberFormatException => 0.0
    }
  }
}

// 简化的咖啡记录案例类
case class CoffeeRecord(
  product: String,
  productType: String,
  state: String,
  market: String,
  marketSize: String,
  coffeeSales: Double,
  profit: Double,
  budgetSales: Double,
  margin: Double,
  totalExpenses: Double
)
