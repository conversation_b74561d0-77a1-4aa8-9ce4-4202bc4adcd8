import org.apache.spark.SparkContext
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession

object CoffeeSalesRanking {
  def main(args: Array[String]): Unit = {
    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("CoffeeSalesRanking")
      .master("local[*]")
      .getOrCreate()
    
    val sc = spark.sparkContext
    
    try {
      // 读取咖啡连锁店数据
      val inputPath = "/home/<USER>/spark02/CoffeeChain.csv"
      val rawData = sc.textFile(inputPath)
      
      // 获取表头并过滤
      val header = rawData.first()
      val dataLines = rawData.filter(line => line != header)
      
      println("开始分析咖啡销售量排名...")
      println("=" * 60)
      
      // 解析数据
      val coffeeData = dataLines.map { line =>
        val fields = parseCSVLine(line)
        (
          fields(4), // Product
          fields(5), // ProductType
          fields(6), // State
          fields(2), // Market
          safeToDouble(fields(12)) // Coffee Sales
        )
      }.filter(_._5 > 0) // 过滤掉销售量为0或负数的记录
      
      // 缓存数据
      coffeeData.cache()
      
      println(s"有效销售记录数: ${coffeeData.count()}")
      
      // 1. 按产品统计总销售量排名
      println("\n1. 产品销售量排名 (Top 10):")
      println("-" * 50)
      val productSales = coffeeData
        .map { case (product, _, _, _, sales) => (product, sales) }
        .reduceByKey(_ + _)
        .sortBy(-_._2)
        .take(10)
      
      productSales.zipWithIndex.foreach { case ((product, totalSales), index) =>
        println(f"${index + 1}%2d. $product%-25s: ${totalSales}%10.2f")
      }
      
      // 2. 按产品类型统计销售量排名
      println("\n2. 产品类型销售量排名:")
      println("-" * 50)
      val productTypeSales = coffeeData
        .map { case (_, productType, _, _, sales) => (productType, sales) }
        .reduceByKey(_ + _)
        .sortBy(-_._2)
        .collect()
      
      productTypeSales.zipWithIndex.foreach { case ((productType, totalSales), index) =>
        println(f"${index + 1}%2d. $productType%-15s: ${totalSales}%10.2f")
      }
      
      // 3. 按州统计销售量排名
      println("\n3. 各州销售量排名:")
      println("-" * 50)
      val stateSales = coffeeData
        .map { case (_, _, state, _, sales) => (state, sales) }
        .reduceByKey(_ + _)
        .sortBy(-_._2)
        .collect()
      
      stateSales.zipWithIndex.foreach { case ((state, totalSales), index) =>
        println(f"${index + 1}%2d. $state%-15s: ${totalSales}%10.2f")
      }
      
      // 4. 按市场统计销售量排名
      println("\n4. 市场销售量排名:")
      println("-" * 50)
      val marketSales = coffeeData
        .map { case (_, _, _, market, sales) => (market, sales) }
        .reduceByKey(_ + _)
        .sortBy(-_._2)
        .collect()
      
      marketSales.zipWithIndex.foreach { case ((market, totalSales), index) =>
        println(f"${index + 1}%2d. $market%-15s: ${totalSales}%10.2f")
      }
      
      // 5. 综合排名：按州和产品类型组合
      println("\n5. 州-产品类型组合销售量排名 (Top 15):")
      println("-" * 60)
      val stateProductTypeSales = coffeeData
        .map { case (_, productType, state, _, sales) => ((state, productType), sales) }
        .reduceByKey(_ + _)
        .sortBy(-_._2)
        .take(15)
      
      stateProductTypeSales.zipWithIndex.foreach { case (((state, productType), totalSales), index) =>
        println(f"${index + 1}%2d. $state%-12s - $productType%-12s: ${totalSales}%10.2f")
      }
      
      // 保存排名结果到文件
      val outputPath = "/home/<USER>/spark02/coffee_sales_ranking"
      
      val rankingResults = sc.parallelize(Seq(
        "咖啡销售量排名分析报告",
        "=" * 50,
        "",
        "1. 产品销售量排名 (Top 10):",
        "-" * 30
      ) ++ productSales.zipWithIndex.map { case ((product, totalSales), index) =>
        f"${index + 1}%2d. $product%-25s: ${totalSales}%10.2f"
      } ++ Seq(
        "",
        "2. 产品类型销售量排名:",
        "-" * 30
      ) ++ productTypeSales.zipWithIndex.map { case ((productType, totalSales), index) =>
        f"${index + 1}%2d. $productType%-15s: ${totalSales}%10.2f"
      } ++ Seq(
        "",
        "3. 各州销售量排名:",
        "-" * 30
      ) ++ stateSales.zipWithIndex.map { case ((state, totalSales), index) =>
        f"${index + 1}%2d. $state%-15s: ${totalSales}%10.2f"
      } ++ Seq(
        "",
        "4. 市场销售量排名:",
        "-" * 30
      ) ++ marketSales.zipWithIndex.map { case ((market, totalSales), index) =>
        f"${index + 1}%2d. $market%-15s: ${totalSales}%10.2f"
      })
      
      rankingResults.coalesce(1).saveAsTextFile(outputPath)
      
      println(s"\n排名结果已保存到: $outputPath")
      
      // 总体统计
      val totalSales = coffeeData.map(_._5).reduce(_ + _)
      val avgSales = totalSales / coffeeData.count()
      
      println(f"\n总体统计:")
      println(f"总销售量: ${totalSales}%.2f")
      println(f"平均销售量: ${avgSales}%.2f")
      println(f"记录总数: ${coffeeData.count()}")
      
    } catch {
      case e: Exception =>
        println(s"分析销售排名时发生错误: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      spark.stop()
    }
  }
  
  // 解析CSV行
  def parseCSVLine(line: String): Array[String] = {
    val fields = scala.collection.mutable.ArrayBuffer[String]()
    var current = new StringBuilder()
    var inQuotes = false
    var i = 0
    
    while (i < line.length) {
      val char = line.charAt(i)
      
      if (char == '"') {
        inQuotes = !inQuotes
      } else if (char == ',' && !inQuotes) {
        fields += current.toString().trim
        current = new StringBuilder()
      } else {
        current += char
      }
      i += 1
    }
    
    fields += current.toString().trim
    fields.toArray
  }
  
  // 安全转换为Double
  def safeToDouble(str: String): Double = {
    try {
      str.replace(",", "").replace("\"", "").toDouble
    } catch {
      case _: NumberFormatException => 0.0
    }
  }
}
