import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._

/**
 * 咖啡销售量排名分析
 * 功能：分析咖啡销售量排名并存储结果
 */
object CoffeeSalesRanking {
  def main(args: Array[String]): Unit = {
    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("CoffeeSalesRanking")
      .master("local[*]")
      .getOrCreate()
    
    import spark.implicits._
    
    // 设置日志级别
    spark.sparkContext.setLogLevel("WARN")
    
    println("开始咖啡销售量排名分析...")
    
    try {
      // 读取清洗后的数据
      val inputPath = "coffee_data_cleaned"
      val coffeeDF = spark.read.parquet(inputPath)
      
      println(s"成功读取数据: $inputPath")
      println(s"数据行数: ${coffeeDF.count()}")
      
      // 1. 按产品销售量排名
      println("\n=== 1. 按产品销售量排名 ===")
      val productSalesRanking = coffeeDF
        .groupBy("Product")
        .agg(
          sum("Coffee Sales").alias("Total_Sales"),
          avg("Coffee Sales").alias("Avg_Sales"),
          count("*").alias("Record_Count"),
          sum("Profit").alias("Total_Profit"),
          avg("Profit").alias("Avg_Profit")
        )
        .orderBy(desc("Total_Sales"))
      
      println("产品销售量排名（前20名）：")
      productSalesRanking.show(20, truncate = false)
      
      // 2. 按州销售量排名
      println("\n=== 2. 按州销售量排名 ===")
      val stateSalesRanking = coffeeDF
        .groupBy("State")
        .agg(
          sum("Coffee Sales").alias("Total_Sales"),
          avg("Coffee Sales").alias("Avg_Sales"),
          count("*").alias("Record_Count"),
          sum("Profit").alias("Total_Profit"),
          avg("Profit").alias("Avg_Profit"),
          countDistinct("Product").alias("Product_Variety")
        )
        .orderBy(desc("Total_Sales"))
      
      println("各州销售量排名：")
      stateSalesRanking.show(truncate = false)
      
      // 3. 按市场规模销售量排名
      println("\n=== 3. 按市场规模销售量排名 ===")
      val marketSalesRanking = coffeeDF
        .groupBy("Market Size")
        .agg(
          sum("Coffee Sales").alias("Total_Sales"),
          avg("Coffee Sales").alias("Avg_Sales"),
          count("*").alias("Record_Count"),
          sum("Profit").alias("Total_Profit"),
          avg("Profit").alias("Avg_Profit")
        )
        .orderBy(desc("Total_Sales"))
      
      println("市场规模销售量排名：")
      marketSalesRanking.show(truncate = false)
      
      // 4. 按产品类型销售量排名
      println("\n=== 4. 按产品类型销售量排名 ===")
      val productTypeSalesRanking = coffeeDF
        .groupBy("Product Type")
        .agg(
          sum("Coffee Sales").alias("Total_Sales"),
          avg("Coffee Sales").alias("Avg_Sales"),
          count("*").alias("Record_Count"),
          sum("Profit").alias("Total_Profit"),
          avg("Profit").alias("Avg_Profit")
        )
        .orderBy(desc("Total_Sales"))
      
      println("产品类型销售量排名：")
      productTypeSalesRanking.show(truncate = false)
      
      // 5. 按年度销售量排名
      println("\n=== 5. 按年度销售量排名 ===")
      val yearSalesRanking = coffeeDF
        .groupBy("Date_Year")
        .agg(
          sum("Coffee Sales").alias("Total_Sales"),
          avg("Coffee Sales").alias("Avg_Sales"),
          count("*").alias("Record_Count"),
          sum("Profit").alias("Total_Profit"),
          avg("Profit").alias("Avg_Profit")
        )
        .orderBy("Date_Year")
      
      println("年度销售量排名：")
      yearSalesRanking.show(truncate = false)
      
      // 6. 综合排名分析（州+产品）
      println("\n=== 6. 综合排名分析（州+产品） ===")
      val stateProductRanking = coffeeDF
        .groupBy("State", "Product")
        .agg(
          sum("Coffee Sales").alias("Total_Sales"),
          avg("Coffee Sales").alias("Avg_Sales"),
          count("*").alias("Record_Count"),
          sum("Profit").alias("Total_Profit")
        )
        .orderBy(desc("Total_Sales"))
      
      println("州+产品销售量排名（前20名）：")
      stateProductRanking.show(20, truncate = false)
      
      // 7. 销售表现分析
      println("\n=== 7. 销售表现分析 ===")
      
      // 计算销售表现指标
      val performanceAnalysis = coffeeDF
        .groupBy("State")
        .agg(
          sum("Coffee Sales").alias("Total_Sales"),
          sum("Budget Sales").alias("Total_Budget_Sales"),
          sum("Profit").alias("Total_Profit"),
          sum("Budget Profit").alias("Total_Budget_Profit"),
          avg("Sales_vs_Budget_Ratio").alias("Avg_Sales_Budget_Ratio"),
          avg("Profit_Margin_Ratio").alias("Avg_Profit_Margin")
        )
        .withColumn("Sales_Achievement_Rate", 
          when(col("Total_Budget_Sales") > 0, 
            col("Total_Sales") / col("Total_Budget_Sales") * 100)
          .otherwise(0))
        .withColumn("Profit_Achievement_Rate", 
          when(col("Total_Budget_Profit") > 0, 
            col("Total_Profit") / col("Total_Budget_Profit") * 100)
          .otherwise(0))
        .orderBy(desc("Sales_Achievement_Rate"))
      
      println("各州销售表现分析：")
      performanceAnalysis.show(truncate = false)
      
      // 8. 保存排名结果
      println("\n=== 8. 保存排名结果 ===")
      
      // 保存产品销售排名
      val productRankingPath = "product_sales_ranking"
      productSalesRanking.write.mode("overwrite").option("header", "true").csv(productRankingPath)
      println(s"产品销售排名已保存到: $productRankingPath")
      
      // 保存州销售排名
      val stateRankingPath = "state_sales_ranking"
      stateSalesRanking.write.mode("overwrite").option("header", "true").csv(stateRankingPath)
      println(s"州销售排名已保存到: $stateRankingPath")
      
      // 保存综合排名
      val comprehensiveRankingPath = "comprehensive_sales_ranking"
      stateProductRanking.write.mode("overwrite").option("header", "true").csv(comprehensiveRankingPath)
      println(s"综合销售排名已保存到: $comprehensiveRankingPath")
      
      // 保存销售表现分析
      val performancePath = "sales_performance_analysis"
      performanceAnalysis.write.mode("overwrite").option("header", "true").csv(performancePath)
      println(s"销售表现分析已保存到: $performancePath")
      
      // 9. 生成排名分析报告
      val reportData = Seq(
        "咖啡销售量排名分析报告",
        "=" * 50,
        "",
        "1. 产品销售排名TOP5:",
        productSalesRanking.take(5).map(row => 
          s"   ${row.getAs[String]("Product")}: ${row.getAs[Double]("Total_Sales").formatted("%.2f")}"
        ).mkString("\n"),
        "",
        "2. 州销售排名TOP5:",
        stateSalesRanking.take(5).map(row => 
          s"   ${row.getAs[String]("State")}: ${row.getAs[Double]("Total_Sales").formatted("%.2f")}"
        ).mkString("\n"),
        "",
        "3. 产品类型销售排名:",
        productTypeSalesRanking.collect().map(row => 
          s"   ${row.getAs[String]("Product Type")}: ${row.getAs[Double]("Total_Sales").formatted("%.2f")}"
        ).mkString("\n"),
        "",
        "4. 市场规模销售排名:",
        marketSalesRanking.collect().map(row => 
          s"   ${row.getAs[String]("Market Size")}: ${row.getAs[Double]("Total_Sales").formatted("%.2f")}"
        ).mkString("\n"),
        "",
        "5. 年度销售对比:",
        yearSalesRanking.collect().map(row => 
          s"   ${row.getAs[Int]("Date_Year")}年: ${row.getAs[Double]("Total_Sales").formatted("%.2f")}"
        ).mkString("\n")
      )
      
      val reportPath = "sales_ranking_report"
      spark.sparkContext.parallelize(reportData).saveAsTextFile(reportPath)
      println(s"排名分析报告已保存到: $reportPath")
      
    } catch {
      case e: Exception =>
        println(s"错误：分析过程中发生异常: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      // 关闭SparkSession
      spark.stop()
    }
    
    println("\n销售量排名分析完成！")
  }
}
